import { computed, onMounted, onUnmounted, readonly } from 'vue';
import { ref } from 'vue';

const DEFAULT_BREAKPOINTS = {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1400,
};
export function useBreakpoints(customBreakpoints = {}) {
    const breakpoints = { ...DEFAULT_BREAKPOINTS, ...customBreakpoints };
    const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024);
    const isClient = ref(typeof window !== 'undefined');

    // Function to update window width
    const updateWidth = () => {
        if (typeof window !== 'undefined') {
            windowWidth.value = window.innerWidth;
        }
    };

    // Debounced update function for better performance
    let timeoutId: ReturnType<typeof setTimeout> | null = null;
    const debouncedUpdateWidth = () => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(updateWidth, 100);
    };

    // Set up event listener on mount
    onMounted(() => {
        isClient.value = true;
        updateWidth();
        if (typeof window !== 'undefined') {
            window.addEventListener('resize', debouncedUpdateWidth);
        }
    });

    // Clean up event listener on unmount
    onUnmounted(() => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        if (typeof window !== 'undefined') {
            window.removeEventListener('resize', debouncedUpdateWidth);
        }
    });

    // Computed properties for each breakpoint
    const xs = computed(() => windowWidth.value >= breakpoints.xs);
    const sm = computed(() => windowWidth.value >= breakpoints.sm);
    const md = computed(() => windowWidth.value >= breakpoints.md);
    const lg = computed(() => windowWidth.value >= breakpoints.lg);
    const xl = computed(() => windowWidth.value >= breakpoints.xl);
    const xxl = computed(() => windowWidth.value >= breakpoints.xxl);

    // Computed properties for "down" breakpoints (less than)
    const xsDown = computed(() => windowWidth.value < breakpoints.sm);
    const smDown = computed(() => windowWidth.value < breakpoints.md);
    const mdDown = computed(() => windowWidth.value < breakpoints.lg);
    const lgDown = computed(() => windowWidth.value < breakpoints.xl);
    const xlDown = computed(() => windowWidth.value < breakpoints.xxl);

    // Computed properties for "up" breakpoints (greater than or equal)
    const xsUp = computed(() => windowWidth.value >= breakpoints.xs);
    const smUp = computed(() => windowWidth.value >= breakpoints.sm);
    const mdUp = computed(() => windowWidth.value >= breakpoints.md);
    const lgUp = computed(() => windowWidth.value >= breakpoints.lg);
    const xlUp = computed(() => windowWidth.value >= breakpoints.xl);
    const xxlUp = computed(() => windowWidth.value >= breakpoints.xxl);

    // Current breakpoint name
    const current = computed(() => {
        if (windowWidth.value >= breakpoints.xxl) return 'xxl';
        if (windowWidth.value >= breakpoints.xl) return 'xl';
        if (windowWidth.value >= breakpoints.lg) return 'lg';
        if (windowWidth.value >= breakpoints.md) return 'md';
        if (windowWidth.value >= breakpoints.sm) return 'sm';
        return 'xs';
    });

    // Mobile/Desktop helpers
    const isMobile = computed(() => windowWidth.value < breakpoints.md);
    const isTablet = computed(() => windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg);
    const isDesktop = computed(() => windowWidth.value >= breakpoints.lg);

    return {
        windowWidth: readonly(windowWidth),
        isClient: readonly(isClient),
        current,
        xs,
        sm,
        md,
        lg,
        xl,
        xxl,
        xsDown,
        smDown,
        mdDown,
        lgDown,
        xlDown,
        xsUp,
        smUp,
        mdUp,
        lgUp,
        xlUp,
        xxlUp,
        isMobile,
        isTablet,
        isDesktop,
        breakpoints: readonly(breakpoints),
    };
}
export function normalizeDate(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

export function compareDates(date1: Date, date2: Date): boolean {
    const normalized1 = normalizeDate(date1);
    const normalized2 = normalizeDate(date2);
    return normalized1.getTime() === normalized2.getTime();
}
