<script setup lang="ts">
import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>ton<PERSON><PERSON>,
    DatePicker,
    Divider,
    Icon,
    InlineGrid,
    OptionList,
    Popover,
    Scrollable,
    Select,
    TextField,
} from '@ownego/polaris-vue';
import ArrowRightIcon from '@shopify/polaris-icons/ArrowRightIcon.svg';
import CalendarIcon from '@shopify/polaris-icons/CalendarIcon.svg';
import { ref, reactive, computed } from 'vue';

import type { DateRange } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { useAnalyticStore } from '@/stores/analyticStore';

import { useBreakpoints, normalizeDate } from './utils';

const analyticStore = useAnalyticStore();
const { mdDown } = useBreakpoints();

const today = normalizeDate(new Date());
const yesterday = normalizeDate(new Date(Date.now() - 24 * 60 * 60 * 1000));
const sevenDaysAgo = normalizeDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
const thirtyDaysAgo = normalizeDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
const ninetyDaysAgo = normalizeDate(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000));
const threeHundredSixtyFiveDaysAgo = normalizeDate(new Date(Date.now() - 365 * 24 * 60 * 60 * 1000));

const lastMonth = new Date();
lastMonth.setDate(1);
lastMonth.setDate(0);
const normalizedLastMonth = normalizeDate(lastMonth);
const firstDayOfLastMonth = normalizeDate(new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1));

const twelveMonthsAgo = new Date();
twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
const normalizedTwelveMonthsAgo = normalizeDate(twelveMonthsAgo);

const lastYearStart = normalizeDate(new Date(today.getFullYear() - 1, 0, 1));
const lastYearEnd = normalizeDate(new Date(today.getFullYear() - 1, 11, 31));

const predefinedRanges = [
    {
        title: 'Today',
        alias: 'today',
        period: {
            since: today,
            until: today,
        },
    },
    {
        title: 'Yesterday',
        alias: 'yesterday',
        period: {
            since: yesterday,
            until: yesterday,
        },
    },
    {
        title: 'Last 7 days',
        alias: 'last7days',
        period: {
            since: sevenDaysAgo,
            until: yesterday,
        },
    },
    {
        title: 'Last 30 days',
        alias: 'last30days',
        period: {
            since: thirtyDaysAgo,
            until: yesterday,
        },
    },
    {
        title: 'Last 90 days',
        alias: 'last90days',
        period: {
            since: ninetyDaysAgo,
            until: yesterday,
        },
    },
    {
        title: 'Last 365 days',
        alias: 'last365days',
        period: {
            since: threeHundredSixtyFiveDaysAgo,
            until: yesterday,
        },
    },
    {
        title: 'Last month',
        alias: 'lastMonth',
        period: {
            since: firstDayOfLastMonth,
            until: normalizedLastMonth,
        },
    },
    {
        title: 'Last 12 months',
        alias: 'last12months',
        period: {
            since: normalizedTwelveMonthsAgo,
            until: yesterday,
        },
    },
    {
        title: 'Last year',
        alias: 'lastYear',
        period: {
            since: lastYearStart,
            until: lastYearEnd,
        },
    },
];

const popoverActive = ref(false);
const datePickerRef = ref<{ $el: Node } | null>(null);
const tempDateRange = ref<DateRange | null>(null);

const activeDateRange = computed(() => analyticStore.currentPeriod);

const sinceDate = computed(() => {
    const currentRange = tempDateRange.value || activeDateRange.value;
    if (!currentRange.period.since) return '';

    const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: '2-digit',
    };
    return currentRange.period.since.toLocaleDateString('en-US', options);
});

const untilDate = computed(() => {
    const currentRange = tempDateRange.value || activeDateRange.value;
    if (!currentRange.period.until) return '';

    const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: '2-digit',
    };
    return currentRange.period.until.toLocaleDateString('en-US', options);
});

const dateState = reactive({
    month: analyticStore.currentPeriod.period.since.getMonth(),
    year: analyticStore.currentPeriod.period.since.getFullYear(),
});

const month = computed(() => dateState.month);
const year = computed(() => dateState.year);

const selectedDates = computed(() => {
    if (tempDateRange.value) {
        return {
            start: tempDateRange.value.period.since,
            end: tempDateRange.value.period.until,
        };
    }

    return {
        start: activeDateRange.value.period.since,
        end: activeDateRange.value.period.until,
    };
});

const buttonValue = computed(() => {
    const currentRange = tempDateRange.value || activeDateRange.value;

    if (currentRange.alias !== 'custom') {
        return currentRange.title;
    }
    return `${sinceDate.value} - ${untilDate.value}`;
});

function setPopoverActive(active: boolean): void {
    popoverActive.value = active;
}

function nodeContainsDescendant(rootNode: Node, descendant: Node): boolean {
    if (rootNode === descendant) {
        return true;
    }
    let parent = descendant.parentNode;
    while (parent != null) {
        if (parent === rootNode) {
            return true;
        }
        parent = parent.parentNode;
    }
    return false;
}

function handleInputBlur(event: FocusEvent): void {
    const currentTarget = event.currentTarget as HTMLInputElement;
    const relatedTarget = event.relatedTarget as Node;
    if (datePickerRef.value && relatedTarget != null) {
        const pickerNode = datePickerRef.value.$el;
        if (nodeContainsDescendant(pickerNode, relatedTarget)) {
            currentTarget.focus();
        }
    }
}

// Date handlers
function handleMonthChange(newMonth: number, newYear: number): void {
    dateState.month = newMonth;
    dateState.year = newYear;
}

function handleCalendarChange(selected: { start: Date; end: Date }): void {
    if (selected.start && selected.end) {
        const newDateRange: DateRange = {
            alias: 'custom',
            title: 'Custom',
            period: {
                since: selected.start,
                until: selected.end,
            },
        };
        tempDateRange.value = newDateRange;
    }
}

// Select and option handlers
function getSelectedArray(): string[] {
    if (tempDateRange.value) {
        return tempDateRange.value.alias === 'custom' ? [] : [tempDateRange.value.alias];
    }
    return activeDateRange.value.alias === 'custom' ? [] : [activeDateRange.value.alias];
}

function getSelectValue(): string {
    if (tempDateRange.value) {
        return tempDateRange.value.alias === 'custom' ? '' : tempDateRange.value.title || tempDateRange.value.alias;
    }
    return activeDateRange.value.alias === 'custom' ? '' : activeDateRange.value.title || activeDateRange.value.alias;
}

function handleSelectChange(value: string): void {
    const selectedRange = predefinedRanges.find((range) => range.alias === value || range.title === value);
    if (selectedRange) {
        tempDateRange.value = selectedRange;
    }
}

function handleOptionListChange(values: string[]): void {
    if (values.length === 0) return;

    const selectedRange = predefinedRanges.find((range) => range.alias === values[0]);
    if (selectedRange) {
        tempDateRange.value = selectedRange;
    }
}

// Button handlers
function apply(): void {
    // Nếu có lựa chọn tạm thời, áp dụng nó
    if (tempDateRange.value) {
        // Cập nhật dateRange trong store
        // Lưu ý: Store sẽ tự động giữ lại activeDataKey
        analyticStore.updateDateRange(tempDateRange.value);

        // Reset biến tạm
        tempDateRange.value = null;
    }
    setPopoverActive(false);
}

function cancel(): void {
    // Hủy lựa chọn tạm thời
    tempDateRange.value = null;
    setPopoverActive(false);
}
</script>

<template>
    <div class="filter-date-section">
        <Popover :active="popoverActive" autofocus-target="none" preferred-alignment="left" preferred-position="below"
            fluid-content :sectioned="false" full-height @close="() => setPopoverActive(false)">
            <template #activator>
                <Button size="slim" :icon="CalendarIcon" @click="() => setPopoverActive(!popoverActive)">
                    {{ buttonValue }}
                </Button>
            </template>

            <Box padding="0">
                <InlineGrid :columns="{
                    xs: '1fr',
                    mdDown: '1fr',
                    md: 'max-content max-content',
                }" :gap="0" ref="datePickerRef">
                    <Box :max-width="mdDown ? '516px' : '212px'" :width="mdDown ? '100%' : '212px'" :padding="150"
                        :padding-block-end="{ xs: 100, md: 0 }" class="date-range-list">
                        <Scrollable v-if="!mdDown" style="height: 334px">
                            <OptionList :padding="0" :options="predefinedRanges.map((range) => ({
                                value: range.alias,
                                label: range.title,
                            }))
                                " :selected="getSelectedArray()" @change="handleOptionListChange" />
                        </Scrollable>
                        <Select v-else label-hidden :value="getSelectValue()"
                            :options="predefinedRanges.map(({ alias, title }) => title || alias)"
                            @change="(value) => handleSelectChange(value)" />
                    </Box>

                    <Box :padding="400" :max-width="mdDown ? '320px' : '516px'" class="calendar-wrapper">
                        <div class="calendar-content">
                            <div class="date-inputs-container">
                                <div class="date-input-wrapper">
                                    <TextField label-hidden :model-value="sinceDate" readonly :autoComplete="'off'"
                                        @blur="handleInputBlur">
                                        <template #prefix>
                                            <Icon :source="CalendarIcon" />
                                        </template>
                                    </TextField>
                                </div>
                                <div class="arrow-wrapper">
                                    <Icon :source="ArrowRightIcon" />
                                </div>
                                <div class="date-input-wrapper">
                                    <TextField label-hidden :model-value="untilDate" readonly :autoComplete="'off'"
                                        @blur="handleInputBlur">
                                        <template #prefix>
                                            <Icon :source="CalendarIcon" />
                                        </template>
                                    </TextField>
                                </div>
                            </div>

                            <div class="calendar-container">
                                <DatePicker :month="month" :year="year" :selected="selectedDates" :multi-month="true"
                                    :month-to-display="2" :disableDatesAfter="today" allow-range
                                    @month-change="handleMonthChange" @change="handleCalendarChange"
                                    v-model="selectedDates" />
                            </div>
                        </div>
                    </Box>
                </InlineGrid>

                <Divider />

                <Box padding="400">
                    <div class="actions-container">
                        <ButtonGroup>
                            <Button @click="cancel">Cancel</Button>
                            <Button variant="primary" @click="apply">Apply</Button>
                        </ButtonGroup>
                    </div>
                </Box>
            </Box>
        </Popover>
    </div>
</template>

<style scoped lang="scss">
.filter-date-section {
    display: block;
}

.date-range-list {
    :deep(.Polaris-OptionList-Option--select) {
        background-color: #f5f5f5;
    }
}

.calendar-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.date-inputs-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .date-input-wrapper {
        width: calc(50% - 15px);

        :deep(.Polaris-TextField) {
            &.Polaris-TextField--readOnly {
                .Polaris-TextField__Input {
                    cursor: default;
                    background-color: #fff;
                }
            }

            .Polaris-TextField__Input {
                height: 36px;
                min-height: 36px;
                font-size: 14px;
                color: #413d3d;
            }

            .Polaris-TextField__Prefix {
                height: 36px;
                display: flex;
                align-items: center;
            }
        }
    }

    .arrow-wrapper {
        display: flex;
        align-items: center;
        padding-top: 10px;
    }
}

.actions-container {
    display: flex;
    justify-content: flex-end;
}
</style>
