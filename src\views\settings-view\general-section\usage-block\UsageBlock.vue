<script setup lang="ts">
import { BlockStack, Badge, InlineStack, Divider, Text, ProgressBar } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars } from '@/locales/settings';

const { t } = useI18n();
</script>

<template>
    <s-grid gridTemplateColumns="@container (inline-size <= 700px) 1fr, 2fr 5fr" gap="base">
        <s-box>
            <s-heading>{{ t(settingsLocaleVars.usageTitle) }}</s-heading>
            <s-paragraph>{{ t(settingsLocaleVars.usageDescription) }}</s-paragraph>
        </s-box>
        <s-section accessibilityLabel="Product defaults section">
            <BlockStack :gap="'600'">
                <BlockStack :gap="'200'">
                    <InlineStack :align="'space-between'">
                        <Text as="span">Number of pages/sections users can publish </Text>
                        <Badge :tone="'info'">
                            <Text as="span">0/1</Text>
                        </Badge>
                    </InlineStack>
                    <ProgressBar :size="'small'" :progress="0" />
                </BlockStack>
                <BlockStack :gap="'200'">
                    <InlineStack :align="'space-between'">
                        <Text as="span">Number of pages/sections users can save to library </Text>
                        <Badge :tone="'info'">
                            <Text as="span">0/ 10 </Text>
                        </Badge>
                    </InlineStack>
                    <ProgressBar :size="'small'" :progress="0" />
                </BlockStack>
                <BlockStack :gap="'200'">
                    <InlineStack :align="'space-between'">
                        <Text as="span">Number of element presets users can save</Text>
                        <Badge :tone="'info'">
                            <Text as="span">10</Text>
                        </Badge>
                    </InlineStack>
                    <Divider />
                </BlockStack>
                <BlockStack :gap="'200'">
                    <InlineStack :align="'space-between'">
                        <Text as="span">Total number of pages/sections users can create </Text>
                        <Badge :tone="'info'">
                            <Text as="span">0/ 11</Text>
                        </Badge>
                    </InlineStack>
                    <ProgressBar :size="'small'" :progress="0" />
                </BlockStack>
                <BlockStack :gap="'200'">
                    <InlineStack :align="'space-between'">
                        <Text as="span">Missing translation </Text>
                        <Badge :tone="'info'">
                            <Text as="span"></Text>
                        </Badge>
                    </InlineStack>
                    <Divider />
                </BlockStack>
            </BlockStack>
        </s-section>
    </s-grid>
</template>

<style scoped lang="scss"></style>
