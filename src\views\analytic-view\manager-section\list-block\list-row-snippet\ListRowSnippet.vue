<script setup lang="ts">
import { IndexTableRow, IndexTableCell, Text, BlockStack, Badge, InlineStack, Icon, Box } from '@ownego/polaris-vue';
import ChartPopularIcon from '@shopify/polaris-icons/ChartPopularIcon.svg';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import { pagesLocaleVars } from '@/locales/pages';

import type { ListRowSnippetProps } from './types';

const { t } = useI18n();
const router = useRouter();
const props = defineProps<ListRowSnippetProps>();
const handleRowClick = (event: Event) => {
    event.stopPropagation();
    const modal = document.getElementById('test-modal') as HTMLDialogElement;
    if (modal) {
        modal.show();
    }
};

const handleIconClick = (event: Event) => {
    event.stopPropagation();
};

const getTranslatedStatus = computed(() => {
    return props.Page.status === 'Published'
        ? t(pagesLocaleVars.pageStatusPublished)
        : props.Page.status === 'Unpublished'
            ? t(pagesLocaleVars.pageStatusUnpublished)
            : props.Page.status;
});





// Computed properties cho các trường analytics
const getAddToCartRate = computed(() => {
    return props.Page.addToCartRate ? `${props.Page.addToCartRate}%` : '-';
});

const getProductViewsRate = computed(() => {
    return props.Page.productViewsRate ? `${props.Page.productViewsRate}%` : '-';
});

const getVisitors = computed(() => {
    return props.Page.visitors ? props.Page.visitors.toLocaleString() : '-';
});

const getSessions = computed(() => {
    return props.Page.sessions ? props.Page.sessions.toLocaleString() : '-';
});

const navigateToAnalytics = (event: Event) => {
    event.stopPropagation();
    router.push(`/analytic/Detail/${props.Page.id}`);
};
</script>

<template>
    <IndexTableRow :id="String(Page.id)" :key="Number(Page.id)" :position="index" :selected="selected">
        <IndexTableCell :class="'cell__wrapper firt-cell'" @click="handleRowClick($event)">
            <BlockStack>
                <Box>
                    <Text variant="bodyMd" fontWeight="medium" as="span" class="clickable-text">{{ Page.title }}</Text>
                </Box>
                <Box>
                    <Text as="span" :tone="'subdued'" font-weight="regular" class="clickable-text">{{ Page.url }}</Text>
                </Box>
            </BlockStack>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper second-cell'" @click="handleRowClick($event)">
            <div class="badge__wrapper">
                <div :class="`badge__wrapper__${Page.status}`">
                    <Badge :progress="Page.status === 'Published' ? 'complete' : 'incomplete'" class="clickable-badge">
                        {{ getTranslatedStatus }}
                    </Badge>
                </div>
            </div>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper third-cell'" @click="handleRowClick($event)">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getAddToCartRate }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fourth-cell'" @click="handleRowClick($event)">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getProductViewsRate }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fifth-cell'" @click="handleRowClick($event)">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getVisitors }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper sixth-cell'" @click="handleRowClick($event)">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getSessions }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper seventh-cell'" @click="handleIconClick">
            <Box>
                <InlineStack :gap="'500'">

                    <a @click="navigateToAnalytics($event)">
                        <Icon :source="ChartPopularIcon"></Icon>
                    </a>
                </InlineStack>
            </Box>
        </IndexTableCell>
    </IndexTableRow>
</template>

<style scoped lang="scss">
.cell__wrapper {
    height: 40px;
}

.clickable-text {
    cursor: pointer;
    display: inline-block;
}

.clickable-badge {
    cursor: pointer;
}

.firt-cell {
    width: 25%;
}

.second-cell {
    width: 10%;
}

.third-cell {
    width: 12%;
}

.fourth-cell {
    width: 12%;
}

.fifth-cell {
    width: 10%;
}

.sixth-cell {
    width: 10%;
}

.seventh-cell {
    width: 15%;
}

.badge__wrapper {
    &__Published {
        .Polaris-Badge {
            background-color: var(--m-success-base-20);
            color: var(--m-success-base-30);

            svg {
                fill: var(--m-success-base-30);
            }
        }
    }

    &__unpublished {
        .Polaris-Badge {
            background-color: var(--m-overlay-color-2);
        }
    }
}
</style>
