import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

import type { ChartItem, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { usePagesStore } from '@/stores/pagesStore';
import { analyticDashboardMock, analyticChartMock } from '@/views/analytic-view/analytic-detail-section/chart-block/config';

import type { PageAnalytic, PageWithAnalytics } from './types';

// Utility function để tạo data cố định dựa trên seed
function seededRandom(seed: string): number {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
        const char = seed.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash) / 2147483647; // Normalize to 0-1
}

// Function để generate consistent data dựa trên pageId và period
function generateConsistentData(pageId: string, period: DateRange, dataType: string): any {
    const seed = `${pageId}-${period.alias}-${dataType}`;
    const random = seededRandom(seed);

    switch (dataType) {
        case 'visitors':
            return Math.floor(random * 5000) + 500; // 500-5500
        case 'sessions':
            return Math.floor(random * 8000) + 800; // 800-8800
        case 'addToCartRate':
            return Math.round((random * 10 + 1) * 10) / 10; // 1.0-11.0%
        case 'productViewsRate':
            return Math.round((random * 30 + 5) * 10) / 10; // 5.0-35.0%
        default:
            return Math.floor(random * 1000);
    }
}

export const useAnalyticStore = defineStore('analytic', () => {
    // Get pagesStore instance
    const pagesStore = usePagesStore();

    // Detail view state
    const items = ref<ChartItem[]>(analyticDashboardMock);
    const isLoading = ref<boolean>(false);
    const error = ref<string | null>(null);
    const activeDataKey = ref<string | null>(null);
    const analyticList = ref<PageAnalytic[]>([]);
    const currentAnalytic = ref<PageAnalytic | null>(null);

    // Manager view state
    const queryValue = ref<string>('');
    const statusFilter = ref<string[]>([]);
    const typeFilter = ref<string>('All');
    const currentPage = ref<number>(1);
    const itemsPerPage = ref<number>(3);

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const currentPeriod = ref<DateRange>({
        alias: 'last7days',
        title: 'Last 7 days',
        period: {
            since: sevenDaysAgo,
            until: yesterday,
        },
    });

    const comparisonMode = ref<'previous_period' | 'previous_year' | 'custom'>('previous_period');

    const graphData = ref<{
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    }>({
        sales: analyticChartMock,
    });

    // Getters
    const getItemByKey = computed(() => {
        return (key: string) => items.value.find((item) => item.key === key);
    });

    const getAnalyticByPageId = computed(() => {
        return (pageId: string) => analyticList.value.find((analytic) => analytic.pageId === pageId);
    });

    const formattedSalesData = computed((): DataPoint[] => {
        if (activeDataKey.value) {
            // Tìm item đang được chọn
            const selectedItem = items.value.find((item) => item.key === activeDataKey.value);

            if (selectedItem) {
                // Lấy giá trị từ item được chọn để điều chỉnh dữ liệu biểu đồ
                const baseValue = selectedItem.value;
                const percentChange = selectedItem.percent / 100; // Chuyển phần trăm thành hệ số

                // Tạo dữ liệu biểu đồ dựa trên giá trị của item
                return graphData.value.sales.map((d, index, arr) => {
                    // Tạo đường cong biểu đồ dựa trên giá trị và phần trăm của item
                    // Giá trị ở đầu là thấp hơn, cuối là cao hơn (để thể hiện sự tăng trưởng)
                    const position = index / (arr.length - 1); // 0 đến 1
                    const trend = Math.pow(position, 1.5); // Tạo đường cong tăng dần

                    // Tính giá trị dựa trên giá trị cơ bản và phần trăm thay đổi
                    const value = baseValue * (0.7 + trend * 0.6 * (1 + percentChange));

                    return {
                        date: new Date(d.date),
                        value: Math.max(5, Math.round(value / 10)), // Chia để đưa về giá trị hợp lý cho biểu đồ
                    };
                });
            }
        }

        // Mặc định trả về dữ liệu sales
        return graphData.value.sales.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
    });

    const formattedComparisonData = computed((): DataPoint[] | null => {
        if (!graphData.value.comparison) return null;

        if (activeDataKey.value) {
            // Tìm item đang được chọn
            const selectedItem = items.value.find((item) => item.key === activeDataKey.value);

            if (selectedItem) {
                // Lấy giá trị từ kỳ trước để làm dữ liệu so sánh
                const baseValue = selectedItem.lastPeriod;

                // Tạo dữ liệu biểu đồ dựa trên giá trị kỳ trước của item
                return graphData.value.comparison.map((d, index, arr) => {
                    // Tạo đường cong biểu đồ dựa trên giá trị kỳ trước
                    // Tạo pattern khác một chút so với dữ liệu chính
                    const position = index / (arr.length - 1); // 0 đến 1
                    const trend = Math.pow(position, 1.2); // Đường cong nhẹ hơn

                    // Tính giá trị dựa trên giá trị kỳ trước
                    const value = baseValue * (0.8 + trend * 0.4);

                    return {
                        date: new Date(d.date),
                        value: Math.max(3, Math.round(value / 10)), // Chia để đưa về giá trị hợp lý cho biểu đồ
                    };
                });
            }
        }

        return graphData.value.comparison.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
    });

    const totalRevenue = computed((): number => {
        const revenueItem = items.value.find((item) => item.key === 'offersRevenue');
        return revenueItem?.value || 0;
    });

    // Actions
    async function fetchAnalyticData(period: DateRange): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            // Lưu lại active key hiện tại
            const currentActiveKey = activeDataKey.value;

            // In a real implementation, this would make an API call
            // For demo purposes, we'll simulate loading time and return mock data
            await new Promise((resolve) => setTimeout(resolve, 200)); // Giảm từ 1000ms xuống 200ms

            // Generate consistent data based on current analytic and period
            if (!currentAnalytic.value) {
                console.warn('No current analytic set, cannot fetch data');
                isLoading.value = false;
                return;
            }

            const pageId = currentAnalytic.value.pageId;
            items.value = analyticDashboardMock.map((item) => {
                const value = generateConsistentData(pageId, period, item.key);
                const lastPeriodValue = generateConsistentData(pageId + '-prev', period, item.key);
                const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                return {
                    ...item,
                    value: value,
                    percent: percent,
                    lastPeriod: lastPeriodValue,
                };
            });

            // Update the current period
            currentPeriod.value = period;

            // Generate random chart data based on the period
            const daysInRange = Math.max(
                Math.round((period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                7,
            );

            const newSalesData = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(period.period.since);
                date.setDate(date.getDate() + i);

                // Generate consistent value based on pageId, period, and day index
                const seed = `${pageId}-${period.alias}-chart-${i}`;
                const random = seededRandom(seed);

                // Create a trend: lower values at start, higher at end
                const trendFactor = (i / (daysInRange - 1)) * 0.5 + 0.5; // 0.5 to 1.0
                const value = Math.floor(random * 15 * trendFactor) + 5;

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            graphData.value = {
                ...graphData.value,
                sales: newSalesData,
            };

            // Khôi phục lại active key
            if (currentActiveKey) {
                // Nếu activeKey không còn tồn tại trong danh sách items mới, chọn item đầu tiên
                const keyExists = items.value.some((item) => item.key === currentActiveKey);
                if (keyExists) {
                    activeDataKey.value = currentActiveKey;
                } else if (items.value.length > 0) {
                    activeDataKey.value = items.value[0].key;
                }
            } else if (items.value.length > 0) {
                // Nếu không có active key trước đó, chọn item đầu tiên
                activeDataKey.value = items.value[0].key;
            }

            // Update analytics data để đồng bộ với manager view
            updateCurrentAnalyticData();
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch analytic data';
        } finally {
            isLoading.value = false;
        }
    }

    function updateDateRange(range: DateRange): void {
        currentPeriod.value = range;
        // Fetch new data based on the updated range
        fetchAnalyticData(range);
    }

    function setComparisonMode(mode: 'previous_period' | 'previous_year' | 'custom'): void {
        comparisonMode.value = mode;
        // In a real implementation, you would re-fetch data with the new comparison mode
        fetchComparisonData();
    }

    async function fetchComparisonData(): Promise<void> {
        if (!currentPeriod.value) return;

        try {
            // Simulate API delay
            await new Promise((resolve) => setTimeout(resolve, 100)); // Giảm delay cho comparison data

            // Generate data for the comparison period
            const period = currentPeriod.value;
            const daysInRange = Math.max(
                Math.round((period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                7,
            );

            let startDate: Date;

            if (comparisonMode.value === 'previous_period') {
                // Previous period of same length
                const periodLength = period.period.until.getTime() - period.period.since.getTime();
                startDate = new Date(period.period.since.getTime() - periodLength);
            } else if (comparisonMode.value === 'previous_year') {
                // Same period last year
                startDate = new Date(period.period.since);
                startDate.setFullYear(startDate.getFullYear() - 1);
            } else {
                // For custom mode, just use a different random seed
                startDate = new Date(period.period.since);
            }

            const pageId = currentAnalytic.value?.pageId || 'default';
            const comparisonData = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);

                // Generate consistent comparison data
                const seed = `${pageId}-${period.alias}-comparison-${comparisonMode.value}-${i}`;
                const random = seededRandom(seed);
                const trendFactor = (i / (daysInRange - 1)) * 0.4 + 0.6; // 0.6 to 1.0
                const value = Math.floor(random * 15 * trendFactor) + 3;

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            graphData.value = {
                ...graphData.value,
                comparison: comparisonData,
            };
        } catch (err) {
            console.error('Failed to fetch comparison data:', err);
        }
    }

    // Set the active data key for the chart
    function setActiveDataKey(key: string): void {
        activeDataKey.value = key;
    }

    // Get active item data
    const activeItemData = computed(() => {
        if (!activeDataKey.value) return null;
        return items.value.find((item) => item.key === activeDataKey.value) || null;
    });

    async function fetchPageAnalytics(): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            // In a real implementation, this would make an API call
            // For demo purposes, we'll simulate loading time and return mock data
            await new Promise((resolve) => setTimeout(resolve, 100)); // Giảm delay cho fetchPageAnalytics

            // Đảm bảo pagesStore có dữ liệu trước
            if (pagesStore.pages.length === 0) {
                pagesStore.fetchPages();
                // Chờ đủ lâu để pagesStore load xong (pagesStore có delay 1000ms)
                await new Promise((resolve) => setTimeout(resolve, 1100)); // Chờ 1.1s
            }

            // Chỉ generate data nếu chưa có hoặc pages thay đổi
            if (analyticList.value.length === 0 || analyticList.value.length !== pagesStore.pages.length) {
                const mockAnalytics: PageAnalytic[] = pagesStore.pages.map((page) => {
                    // Generate consistent data for each page với default period
                    const addToCartRate = generateConsistentData(page.id, currentPeriod.value, 'addToCartRate');
                    const productViewsRate = generateConsistentData(page.id, currentPeriod.value, 'productViewsRate');
                    const visitors = generateConsistentData(page.id, currentPeriod.value, 'visitors');
                    const sessions = generateConsistentData(page.id, currentPeriod.value, 'sessions');

                return {
                    id: page.id, // Analytics ID trùng với page ID (1:1 relationship)
                    pageId: page.id,

                    // Analytics metrics - consistent data
                    addToCartRate,
                    productViewsRate,
                    visitors,
                    sessions,

                    // Detail view data - consistent based on page
                    items: analyticDashboardMock.map((item) => {
                        const value = generateConsistentData(page.id, currentPeriod.value, item.key);
                        const lastPeriodValue = generateConsistentData(page.id + '-prev', currentPeriod.value, item.key);
                        const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                        return {
                            ...item,
                            value,
                            percent,
                            lastPeriod: lastPeriodValue,
                        };
                    }),
                    graphData: {
                        sales: Array.from({ length: 7 }, (_, j) => {
                            const date = new Date();
                            date.setDate(date.getDate() - 7 + j);
                            const seed = `${page.id}-${currentPeriod.value.alias}-chart-${j}`;
                            const random = seededRandom(seed);
                            const trendFactor = (j / 6) * 0.5 + 0.5; // 0.5 to 1.0
                            const value = Math.floor(random * 15 * trendFactor) + 5;

                            return {
                                date: date.toISOString().split('T')[0],
                                value,
                            };
                        }),
                    },
                    created_at: new Date(Date.now() - seededRandom(page.id + '-created') * 10000000000).toISOString(),
                    updated_at: new Date().toISOString(),
                };
                });

                analyticList.value = mockAnalytics;

                // Set current analytic to the first one if none is selected
                if (!currentAnalytic.value && mockAnalytics.length > 0) {
                    currentAnalytic.value = mockAnalytics[0];
                }
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch page analytics';
        } finally {
            isLoading.value = false;
        }
    }

    function setCurrentAnalytic(analyticId: string): void {
        const analytic = analyticList.value.find((a) => a.id === analyticId);
        if (analytic) {
            currentAnalytic.value = analytic;

            // Update items and graphData based on the selected analytic
            items.value = analytic.items;
            graphData.value = analytic.graphData;

            // Set active key to the first item if available
            if (items.value.length > 0) {
                activeDataKey.value = items.value[0].key;
            }
        }
    }

    // Function để update analytics data từ detail view
    function updateCurrentAnalyticData(): void {
        if (currentAnalytic.value) {
            const analyticIndex = analyticList.value.findIndex(a => a.id === currentAnalytic.value!.id);
            if (analyticIndex !== -1) {
                // Update analytics data với dữ liệu mới từ detail view
                analyticList.value[analyticIndex] = {
                    ...analyticList.value[analyticIndex],
                    items: items.value,
                    graphData: graphData.value,
                    // Update metrics từ items
                    visitors: items.value.find(item => item.key === 'visitors')?.value || analyticList.value[analyticIndex].visitors,
                    sessions: items.value.find(item => item.key === 'sessions')?.value || analyticList.value[analyticIndex].sessions,
                    addToCartRate: items.value.find(item => item.key === 'addToCartRate')?.value || analyticList.value[analyticIndex].addToCartRate,
                    productViewsRate: items.value.find(item => item.key === 'productViewsRate')?.value || analyticList.value[analyticIndex].productViewsRate,
                    updated_at: new Date().toISOString(),
                };

                // Update currentAnalytic reference
                currentAnalytic.value = analyticList.value[analyticIndex];
            }
        }
    }

    // Computed property để kết hợp page data với analytics data
    const pagesWithAnalytics = computed((): PageWithAnalytics[] => {
        return pagesStore.pages.map(page => {
            const analytics = analyticList.value.find(a => a.pageId === page.id);

            if (!analytics) {
                // Tạo analytics mặc định nếu chưa có
                const defaultAnalytics: PageAnalytic = {
                    id: page.id,
                    pageId: page.id,
                    addToCartRate: 0,
                    productViewsRate: 0,
                    visitors: 0,
                    sessions: 0,
                    items: [],
                    graphData: { sales: [] },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                };
                return {
                    ...page,
                    analytics: defaultAnalytics
                };
            }

            // Đồng bộ dữ liệu từ currentPeriod và items nếu đây là currentAnalytic
            let syncedAnalytics = analytics;
            if (currentAnalytic.value && currentAnalytic.value.id === analytics.id) {
                // Sync dữ liệu từ detail view state
                syncedAnalytics = {
                    ...analytics,
                    items: items.value, // Dữ liệu từ detail view
                    graphData: graphData.value, // Chart data từ detail view
                    // Sync metrics từ items nếu có
                    visitors: items.value.find(item => item.key === 'visitors')?.value || analytics.visitors,
                    sessions: items.value.find(item => item.key === 'sessions')?.value || analytics.sessions,
                    addToCartRate: items.value.find(item => item.key === 'addToCartRate')?.value || analytics.addToCartRate,
                    productViewsRate: items.value.find(item => item.key === 'productViewsRate')?.value || analytics.productViewsRate,
                };
            }

            return {
                ...page,
                analytics: syncedAnalytics
            };
        });
    });

    // Manager view computed properties
    const filteredAnalytics = computed(() => {
        let filtered = [...pagesWithAnalytics.value];

        // Filter by type
        if (typeFilter.value && typeFilter.value !== 'All') {
            filtered = filtered.filter((pageWithAnalytics) => pageWithAnalytics.type === typeFilter.value);
        }

        // Filter by status
        if (statusFilter.value.length > 0) {
            if (statusFilter.value.includes('All') && statusFilter.value.length === 1) {
                // Do nothing, show all
            } else {
                filtered = filtered.filter((pageWithAnalytics) => statusFilter.value.includes(pageWithAnalytics.status));
            }
        }

        // Filter by query
        if (queryValue.value) {
            filtered = filtered.filter((pageWithAnalytics) =>
                pageWithAnalytics.title.toLowerCase().includes(queryValue.value.toLowerCase())
            );
        }

        return filtered;
    });

    const paginatedAnalytics = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredAnalytics.value.slice(start, end);
    });

    const totalPages = computed(() =>
        Math.max(1, Math.ceil(filteredAnalytics.value.length / itemsPerPage.value))
    );

    const totalAnalytics = computed(() => filteredAnalytics.value.length);

    // Manager view actions
    function setQueryValue(value: string): void {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatusFilter(statuses: string[]): void {
        statusFilter.value = statuses;
        currentPage.value = 1;
    }

    function setTypeFilter(type: string): void {
        typeFilter.value = type;
        currentPage.value = 1;
    }

    function setCurrentPage(page: number): void {
        currentPage.value = page;
    }

    function goToPreviousPage(): void {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage(): void {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updateAnalyticStatus(id: string, newStatus: string): void {
        // Update status trong pagesStore vì status thuộc về page data
        pagesStore.updatePageStatus(id, newStatus);
    }

    function getAnalyticById(id: string): PageAnalytic | undefined {
        return analyticList.value.find((analytic) => analytic.id === id);
    }

    // Function để load data ngay lập tức từ existing analytic
    function loadAnalyticDataInstantly(analyticId: string): boolean {
        const analytic = getAnalyticById(analyticId);
        if (analytic && analytic.items && analytic.items.length > 0) {
            // Load data ngay lập tức
            items.value = [...analytic.items]; // Clone để tránh reference issues
            graphData.value = { ...analytic.graphData };
            currentAnalytic.value = analytic;

            // Set active key
            if (analytic.items.length > 0) {
                activeDataKey.value = analytic.items[0].key;
            }

            return true; // Đã load thành công
        }
        return false; // Chưa có data, cần fetch
    }

    // Function để preload data cho một page (gọi khi hover hoặc navigate)
    function preloadAnalyticData(pageId: string): void {
        const analytic = getAnalyticByPageId.value(pageId);
        if (analytic && (!analytic.items || analytic.items.length === 0)) {
            // Background fetch data cho page này
            const tempCurrentAnalytic = currentAnalytic.value;
            currentAnalytic.value = analytic;
            fetchAnalyticData(currentPeriod.value).then(() => {
                // Restore current analytic
                currentAnalytic.value = tempCurrentAnalytic;
            });
        }
    }

    // Function để update tất cả analytics data khi period thay đổi
    function updateAllAnalyticsForPeriod(period: DateRange): void {
        // Kiểm tra nếu analyticList rỗng thì không làm gì
        if (!analyticList.value || analyticList.value.length === 0) {
            return;
        }

        // Tạo array mới để trigger reactivity
        analyticList.value = analyticList.value.map(analytic => {
            // Update metrics cho mỗi analytic dựa trên period mới
            const pageId = analytic.pageId;
            const visitors = generateConsistentData(pageId, period, 'visitors');
            const sessions = generateConsistentData(pageId, period, 'sessions');
            const addToCartRate = generateConsistentData(pageId, period, 'addToCartRate');
            const productViewsRate = generateConsistentData(pageId, period, 'productViewsRate');

            // Update items data cho detail view
            const items = analyticDashboardMock.map((item) => {
                const value = generateConsistentData(pageId, period, item.key);
                const lastPeriodValue = generateConsistentData(pageId + '-prev', period, item.key);
                const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                return {
                    ...item,
                    value,
                    percent,
                    lastPeriod: lastPeriodValue,
                };
            });

            // Update graph data - chỉ update sales data, giữ nguyên structure
            const newSalesData = analyticChartMock.map((_, index) => {
                const date = new Date();
                date.setDate(date.getDate() - (analyticChartMock.length - 1 - index));

                // Generate consistent value based on pageId and period
                const seed = `${pageId}-${period.alias}-sales-${index}`;
                const random = seededRandom(seed);
                const value = Math.round(50 + random * 200); // 50-250 range

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            const graphData = {
                sales: newSalesData,
            };

            // Return new object để trigger reactivity
            const updatedAnalytic = {
                ...analytic,
                visitors,
                sessions,
                addToCartRate,
                productViewsRate,
                items,
                graphData,
                updated_at: new Date().toISOString(),
            };

            return updatedAnalytic;
        });

        // Nếu có currentAnalytic đang được hiển thị, cũng update nó
        if (currentAnalytic.value) {
            const updatedCurrentAnalytic = analyticList.value.find(a => a.id === currentAnalytic.value!.id);
            if (updatedCurrentAnalytic) {
                currentAnalytic.value = updatedCurrentAnalytic;
                // Sync với detail view state
                items.value = updatedCurrentAnalytic.items;
                graphData.value = updatedCurrentAnalytic.graphData;
            }
        }
    }

    return {
        // Detail view state
        items,
        currentPeriod,
        isLoading,
        error,
        comparisonMode,
        graphData,
        activeDataKey,
        analyticList,
        currentAnalytic,

        // Manager view state
        queryValue,
        statusFilter,
        typeFilter,
        currentPage,
        itemsPerPage,

        // Detail view getters
        getItemByKey,
        getAnalyticByPageId,
        getAnalyticById,
        formattedSalesData,
        formattedComparisonData,
        totalRevenue,
        activeItemData,

        // Manager view getters
        filteredAnalytics,
        paginatedAnalytics,
        totalPages,
        totalAnalytics,

        // Detail view actions
        fetchAnalyticData,
        updateDateRange,
        setComparisonMode,
        fetchComparisonData,
        setActiveDataKey,
        fetchPageAnalytics,
        setCurrentAnalytic,
        updateCurrentAnalyticData,
        loadAnalyticDataInstantly,
        preloadAnalyticData,
        generateConsistentData,
        updateAllAnalyticsForPeriod,

        // Manager view actions
        setQueryValue,
        setStatusFilter,
        setTypeFilter,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updateAnalyticStatus,
    };
});
