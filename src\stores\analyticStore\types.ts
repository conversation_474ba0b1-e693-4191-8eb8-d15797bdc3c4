import type { ChartItem, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

export interface PageAnalytic {
    id: string; // ID của analytics này
    pageId: string; // ID của page liên kết
    title: string; // Tiêu đ<PERSON>, thường là tên của page
    status: string; // Trạng thái page (Published/Unpublished)
    type?: string; // Loại page (Landing, Home, Product, Collection)
    url?: string; // URL của page

    // Metrics tổng quan cho manager view
    addToCartRate: number; // Tỷ lệ thêm vào giỏ hàng (%)
    productViewsRate: number; // Tỷ lệ xem sản phẩm (%)
    visitors: number; // Số lượng khách truy cập
    sessions: number; // Số phiên làm việc

    // Dữ liệu chi tiết cho analytic detail view
    items: ChartItem[]; // D<PERSON> liệu thống kê chi tiết của page
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    created_at: string;
    updated_at: string;
}

export interface AnalyticState {
    items: ChartItem[]; // Chart items hiện tại được hiển thị
    currentPeriod: DateRange;
    isLoading: boolean;
    error: string | null;
    comparisonMode: 'previous_period' | 'previous_year' | 'custom';
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    analyticList: PageAnalytic[]; // Danh sách tất cả các analytics
    currentAnalytic: PageAnalytic | null; // Analytics hiện tại đang được hiển thị

    // Manager view state
    queryValue: string;
    statusFilter: string[];
    typeFilter: string;
    currentPage: number;
    itemsPerPage: number;
}

export interface AnalyticGetters {
    getItemByKey: (_key: string) => ChartItem | undefined;
    formattedSalesData: DataPoint[];
    formattedComparisonData: DataPoint[] | null;
    totalRevenue: number;
    getAnalyticByPageId: (_pageId: string) => PageAnalytic | undefined;
    getAnalyticById: (_id: string) => PageAnalytic | undefined;

    // Manager view getters
    filteredAnalytics: PageAnalytic[];
    paginatedAnalytics: PageAnalytic[];
    totalPages: number;
    totalAnalytics: number;
}

export interface AnalyticActions {
    fetchAnalyticData: (_period: DateRange) => Promise<void>;
    updateDateRange: (_range: DateRange) => void;
    setComparisonMode: (_mode: 'previous_period' | 'previous_year' | 'custom') => void;
    fetchPageAnalytics: () => Promise<void>;
    setCurrentAnalytic: (_analyticId: string) => void;
}
