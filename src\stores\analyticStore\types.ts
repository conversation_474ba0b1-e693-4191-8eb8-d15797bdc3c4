import type { ChartI<PERSON>, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

// Re-export types for convenience
export type { DateRange, ChartItem, DataPoint };

export interface PageAnalytic {
    id: string; // ID của analytics này (trùng với pageId để đảm bảo 1:1)
    pageId: string; // ID của page liên kết (1:1 relationship)

    // Analytics metrics - chỉ chứa dữ liệu analytics thuần túy
    addToCartRate: number; // Tỷ lệ thêm vào giỏ hàng (%)
    productViewsRate: number; // Tỷ lệ xem sản phẩm (%)
    visitors: number; // Số lượng khách truy cập
    sessions: number; // Số phiên làm việc

    // Dữ liệu chi tiết cho analytic detail view
    items: ChartItem[]; // Dữ liệu thống kê chi tiết của page
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    created_at: string;
    updated_at: string;
}

// Interface để kết hợp page data với analytics data cho display
export interface PageWithAnalytics {
    // Page data (từ pagesStore)
    id: string;
    title: string;
    status: string;
    type: string;
    url: string;
    updated_at: string;

    // Analytics data (từ analyticStore)
    analytics: PageAnalytic;
}

export interface AnalyticState {
    items: ChartItem[]; // Chart items hiện tại được hiển thị
    currentPeriod: DateRange;
    isLoading: boolean;
    error: string | null;
    comparisonMode: 'previous_period' | 'previous_year' | 'custom';
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    analyticList: PageAnalytic[]; // Danh sách tất cả các analytics
    currentAnalytic: PageAnalytic | null; // Analytics hiện tại đang được hiển thị

    // Manager view state
    queryValue: string;
    statusFilter: string[];
    typeFilter: string;
    currentPage: number;
    itemsPerPage: number;
}

export interface AnalyticGetters {
    getItemByKey: (_key: string) => ChartItem | undefined;
    formattedSalesData: DataPoint[];
    formattedComparisonData: DataPoint[] | null;
    totalRevenue: number;
    getAnalyticByPageId: (_pageId: string) => PageAnalytic | undefined;
    getAnalyticById: (_id: string) => PageAnalytic | undefined;

    // Manager view getters
    filteredAnalytics: PageAnalytic[];
    paginatedAnalytics: PageAnalytic[];
    totalPages: number;
    totalAnalytics: number;
}

export interface AnalyticActions {
    fetchAnalyticData: (_period: DateRange) => Promise<void>;
    updateDateRange: (_range: DateRange) => void;
    setComparisonMode: (_mode: 'previous_period' | 'previous_year' | 'custom') => void;
    fetchPageAnalytics: () => Promise<void>;
    setCurrentAnalytic: (_analyticId: string) => void;
}
