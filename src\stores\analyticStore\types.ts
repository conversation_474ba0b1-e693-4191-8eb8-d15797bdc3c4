import type { ChartItem, DateRange, DataPoint } from '@/views/analytic-view/chart-section/types';

export interface PageAnalytic {
    id: string; // ID của analytics này
    pageId: string; // ID của page liên kết
    title: string; // Ti<PERSON><PERSON> đ<PERSON>, thường là tên của page
    items: ChartItem[]; // Dữ liệu thống kê của page
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    created_at: string;
    updated_at: string;
}

export interface AnalyticState {
    items: ChartItem[]; // Chart items hiện tại được hiển thị
    currentPeriod: DateRange;
    isLoading: boolean;
    error: string | null;
    comparisonMode: 'previous_period' | 'previous_year' | 'custom';
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    analyticList: PageAnalytic[]; // <PERSON>h sách tất cả các analytics
    currentAnalytic: PageAnalytic | null; // Analytics hiện tại đang đ<PERSON>c hiển thị
}

export interface AnalyticGetters {
    getItemByKey: (_key: string) => ChartItem | undefined;
    formattedSalesData: DataPoint[];
    formattedComparisonData: DataPoint[] | null;
    totalRevenue: number;
    getAnalyticByPageId: (_pageId: string) => PageAnalytic | undefined;
}

export interface AnalyticActions {
    fetchAnalyticData: (_period: DateRange) => Promise<void>;
    updateDateRange: (_range: DateRange) => void;
    setComparisonMode: (_mode: 'previous_period' | 'previous_year' | 'custom') => void;
    fetchPageAnalytics: () => Promise<void>;
    setCurrentAnalytic: (_analyticId: string) => void;
}
