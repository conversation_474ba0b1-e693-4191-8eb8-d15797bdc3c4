export interface PageAnalyticItem {
    id: string;
    title: string;
    status: string;
    addToCartRate: number; // Tỷ lệ thêm vào giỏ hàng (%)
    productViewsRate: number; // Tỷ lệ xem sản phẩm (%)
    visitors: number; // Số lượng khách truy cập
    sessions: number; // Số phiên làm việc
    type?: string; // Lo<PERSON>i trang (optional để tương thích)
    url?: string; // URL trang (optional để tương thích)
    updated_at?: string; // Thời gian cập nhật (optional để tương thích)
    analyticId?: string; // ID analytics liên kết
}

export interface PageAnalyticStoreState {
    pageAnalytics: PageAnalyticItem[];
    isLoading: boolean;
    queryValue: string;
    status: string[];
    currentPage: number;
    itemsPerPage: number;
    activeTab: string;
}

export interface SortOption {
    label: string;
    value: string;
    directionLabel: string;
}

export interface FilterChoice {
    label: string;
    value: string;
}
