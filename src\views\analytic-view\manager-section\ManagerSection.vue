<script setup lang="ts">
import { Card } from '@ownego/polaris-vue';
import { ref, computed, onMounted, watch } from 'vue';

import type { PageAnalyticItem } from '@/stores/pageAnalyticStore';

import { usePageAnalyticStore } from '@/stores/pageAnalyticStore';

import { resourceName, tableHeadings, PageStatusOptions, tabs, PageAnalytics } from './configs';
import { ListBlock } from './list-block';
import { EmptyStateSnippet } from './list-block/empty-state-snippet';
import { PaginateBlock } from './paginate-block';
import { SkeletonBlock } from './skeleton-block';
import { useFilters } from './utils';

const pageAnalyticStore = usePageAnalyticStore();

onMounted(() => {
    if (pageAnalyticStore.pageAnalytics.length === 0) {
        pageAnalyticStore.isLoading = true;

        setTimeout(() => {
            const convertedPageAnalytics: PageAnalyticItem[] = PageAnalytics.map((page) => ({
                id: page.id,
                title: page.title,
                status: page.status,
                addToCartRate: page.addToCartRate,
                productViewsRate: page.productViewsRate,
                visitors: page.visitors,
                sessions: page.sessions,
                type: page.type,
                url: page.url,
                updated_at: page.updated_at,
                analyticId: page.analyticId,
            }));

            pageAnalyticStore.pageAnalytics = convertedPageAnalytics;
            pageAnalyticStore.isLoading = false;
        }, 1000);
    }
});

const activeTabId = ref(tabs[0]?.id);
const selected = ref(0);
const { queryValue, status, handleStatus, appliedFilters } = useFilters();

const listBlockRef = ref();

const typeMap: Record<string, string | undefined> = {
    landing: 'Landing',
    home: 'Home',
    product: 'Product',
    collection: 'Collection',
    'list-collection': 'List collection',
};

const filteredData = computed(() => {
    let filtered = [...pageAnalyticStore.pageAnalytics];
    const activePanelID = tabs.find((tab) => tab.id === activeTabId.value)?.panelID;

    if (activePanelID && activePanelID !== 'all') {
        const typeFilter = typeMap[activePanelID];
        if (typeFilter) filtered = filtered.filter((page) => page.type === typeFilter);
    }

    const selectedOption = PageStatusOptions[selected.value];
    if (selectedOption?.content !== 'All') {
        filtered = filtered.filter((page) => page.status === selectedOption.content);
    }

    if (queryValue.value) {
        filtered = filtered.filter((page) => page.title.toLowerCase().includes(queryValue.value.toLowerCase()));
    }

    if (status.value.length > 0) {
        if (status.value.includes('All') && status.value.length === 1) return filtered;
        filtered = filtered.filter((page) => status.value.includes(page.status));
    }

    listBlockRef.value?.clearSelection();
    return filtered;
});

const currentPage = computed({
    get: () => pageAnalyticStore.currentPage,
    set: (value) => pageAnalyticStore.setCurrentPage(value),
});

const paginatedData = computed(() => {
    const startIndex = (currentPage.value - 1) * pageAnalyticStore.itemsPerPage;
    const endIndex = startIndex + pageAnalyticStore.itemsPerPage;
    return filteredData.value.slice(startIndex, endIndex);
});

const totalPages = computed(() => {
    return Math.max(1, Math.ceil(filteredData.value.length / pageAnalyticStore.itemsPerPage));
});

const goToPreviousPage = () => {
    pageAnalyticStore.goToPreviousPage();
};

const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
        pageAnalyticStore.goToNextPage();
    }
};



const handleFiltersSelect = (index: number) => {
    selected.value = index;
    pageAnalyticStore.setCurrentPage(1);
};

const handleFiltersQueryChange = (value: string) => {
    pageAnalyticStore.setQueryValue(value);
    pageAnalyticStore.setCurrentPage(1);
};

const updateStatus = (ids: string[], newStatus: string) => {
    ids.forEach((id) => {
        pageAnalyticStore.updatePageAnalyticStatus(id, newStatus);
    });
};

watch(paginatedData, () => {
    listBlockRef.value?.clearSelection();
});
</script>

<template>
    <SkeletonBlock v-if="pageAnalyticStore.isLoading" />

    <template v-else>
        <div v-if="pageAnalyticStore.pageAnalytics.length > 0" class="analytics-container">
            <!-- Analytics Summary -->

            <!-- Main Data Table -->
            <Card padding="none">
                <ListBlock ref="listBlockRef" :status="status" :appliedFilters="appliedFilters" :Page="paginatedData"
                    :resourceName="resourceName" :tableHeadings="tableHeadings" :PageStatusOptions="PageStatusOptions"
                    :selected="selected" :handleFiltersSelect="handleFiltersSelect" :update-status="updateStatus"
                    :handleFiltersQueryChange="handleFiltersQueryChange" :handleStatus="handleStatus" />
                <PaginateBlock :currentPage="currentPage" :totalPages="totalPages" :totalItems="filteredData.length"
                    :startItem="(currentPage - 1) * pageAnalyticStore.itemsPerPage + 1"
                    :endItem="Math.min(currentPage * pageAnalyticStore.itemsPerPage, filteredData.length)"
                    :goToPreviousPage="goToPreviousPage" :goToNextPage="goToNextPage" />
            </Card>
        </div>

        <Card v-else padding="none">
            <EmptyStateSnippet />
        </Card>
    </template>
</template>

<style scoped lang="scss">
.analytics-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
</style>
