export interface FormItem {
    form_id: string;
    formtitle: string;
    status: 'Published' | 'Unpublished';
    submissions_count: number;
    Last_updated: string;
}

export interface FormsStoreState {
    forms: FormItem[];
    isLoading: boolean;
    queryValue: string;
    status: string[];
    currentPage: number;
    itemsPerPage: number;
}

export interface FormsStoreGetters {
    filteredForms: (_state: FormsStoreState) => FormItem[];
    paginatedForms: (_state: FormsStoreState) => FormItem[];
    totalPages: (_state: FormsStoreState) => number;
    totalItems: (_state: FormsStoreState) => number;
    startItem: (_state: FormsStoreState) => number;
    endItem: (_state: FormsStoreState) => number;
}

export interface FormsStoreActions {
    fetchForms: () => void;
    setQueryValue: (_value: string) => void;
    setStatus: (_status: string[]) => void;
    clearStatus: () => void;
    clearQueryValue: () => void;
    clearAllFilters: () => void;
    setCurrentPage: (_page: number) => void;
    goToNextPage: () => void;
    goToPreviousPage: () => void;
    updateStatus: (_ids: string[], _newStatus: string) => void;
    setIsLoading: (_value: boolean) => void;
}
