<script setup lang="ts">
import { Badge, Box, InlineStack, Text } from '@ownego/polaris-vue';

interface ChartItem {
    key: string;
    label: string;
    value: number;
    percent: number;
    lastPeriod: number;
    unit: string;
}

defineProps<{
    item: ChartItem;
}>();

function formatValue(value: number, item?: ChartItem): string {
    const unit = item?.unit || '';

    if (unit === '%') {
        return value.toFixed(1) + unit;
    }

    if (value >= 1000) {
        return value.toLocaleString() + unit;
    }

    return value.toString() + unit;
}
</script>

<template>
    <Box :padding="300" :border-radius="200">
        <div class="chart-item-content">
            <InlineStack padding="200" :align="'start'">
                <div class="item-title">
                    <Text as="p" variant="bodySm" fontWeight="medium" tone="subdued">
                        {{ item.label }}
                    </Text>
                </div>
            </InlineStack>
            <div class="item-values">
                <InlineStack :gap="200">
                    <span class="item-value">
                        {{ formatValue(item.value, item) }}
                    </span>
                    <Badge :tone="item.percent > 0 ? 'success' : 'warning'">
                        {{ item.percent > 0 ? '+' : '' }}{{ item.percent }}%
                    </Badge>
                </InlineStack>

            </div>
        </div>
    </Box>
</template>

<style scoped>
.chart-item-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.item-values {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-title {
    border-bottom: var(--p-border-width-050) dotted var(--p-color-border);
    padding-bottom: 8px;
}
</style>
