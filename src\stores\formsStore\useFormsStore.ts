import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

import { FormDatas, FormStatusOptions } from '@/views/forms-view/configs';

import type { FormItem } from './types';

export const useFormsStore = defineStore('forms', () => {
    // state
    const forms = ref<FormItem[]>([...FormDatas] as FormItem[]);
    const isLoading = ref(false);
    const queryValue = ref('');
    const status = ref<string[]>([]);
    const currentPage = ref(1);
    const itemsPerPage = ref(3);
    const selectedFilterIndex = ref(0);

    // getters
    const filteredForms = computed(() => {
        let filtered = [...forms.value];

        // Logic lọc theo tab (selected index)
        const selectedOption = FormStatusOptions[selectedFilterIndex.value];
        if (selectedOption && selectedOption.content !== 'All') {
            filtered = filtered.filter((item) => item.status === selectedOption.content);
        }

        // Logic lọc theo query value (search)
        if (queryValue.value) {
            filtered = filtered.filter((item) =>
                item.formtitle?.toLowerCase().includes(queryValue.value.toLowerCase()),
            );
        }

        // Logic lọc theo status (filter tags)
        if (status.value.length > 0) {
            if (status.value.includes('All') && status.value.length === 1) {
                // Không làm gì nếu chỉ chọn "All"
            } else {
                filtered = filtered.filter((item) => status.value.includes(item.status));
            }
        }

        return filtered;
    });

    const paginatedForms = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredForms.value.slice(start, end);
    });

    const totalPages = computed(() => Math.max(1, Math.ceil(filteredForms.value.length / itemsPerPage.value)));

    const totalItems = computed(() => filteredForms.value.length);

    const startItem = computed(() => {
        if (filteredForms.value.length === 0) return 0;
        return (currentPage.value - 1) * itemsPerPage.value + 1;
    });

    const endItem = computed(() => {
        if (filteredForms.value.length === 0) return 0;
        return Math.min(startItem.value + itemsPerPage.value - 1, totalItems.value);
    });

    // actions
    function fetchForms() {
        isLoading.value = true;
        // Giả lập API call
        setTimeout(() => {
            forms.value = [...FormDatas];
            isLoading.value = false;
        }, 1000);
    }

    function setQueryValue(value: string) {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatus(newStatus: string[]) {
        status.value = newStatus;
        currentPage.value = 1;
    }

    function setSelectedFilterIndex(index: number) {
        selectedFilterIndex.value = index;
        currentPage.value = 1;
    }

    function clearStatus() {
        status.value = [];
    }

    function clearQueryValue() {
        queryValue.value = '';
    }

    function clearAllFilters() {
        status.value = [];
        queryValue.value = '';
        selectedFilterIndex.value = 0; // Reset về "All"
    }

    function setCurrentPage(page: number) {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
        }
    }

    function goToPreviousPage() {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage() {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updateStatus(ids: string[], newStatus: string) {
        forms.value = forms.value.map((item) => {
            if (ids.includes(item.form_id)) {
                return { ...item, status: newStatus as 'Published' | 'Unpublished' };
            }
            return item;
        });
    }

    function setIsLoading(value: boolean) {
        isLoading.value = value;
    }

    return {
        // state
        forms,
        isLoading,
        queryValue,
        status,
        currentPage,
        itemsPerPage,
        selectedFilterIndex,
        // getters
        filteredForms,
        paginatedForms,
        totalPages,
        totalItems,
        startItem,
        endItem,
        // actions
        fetchForms,
        setQueryValue,
        setStatus,
        setSelectedFilterIndex,
        clearStatus,
        clearQueryValue,
        clearAllFilters,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updateStatus,
        setIsLoading,
    };
});
