import type { Pages, FilterChoice, PageAnalytic } from './types';

export const Page: Pages[] = [
    {
        id: '1020',
        title: 'Regular page 1',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1019',
        title: 'Home page 1',
        url: '/',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1018',
        title: 'Product page 1',
        url: '/product/1',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1021',
        title: 'Regular page 2',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 27, 2024',
    },
    {
        id: '1022',
        title: 'Home page 2',
        url: '/home-2',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Nov 28, 2024',
    },
    {
        id: '1023',
        title: 'Product page 2',
        url: '/product/2',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Nov 29, 2024',
    },
    {
        id: '1024',
        title: 'Landing page 1',
        url: '/landing/1',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 30, 2024',
    },
    {
        id: '1025',
        title: 'Contact page',
        url: '/contact',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 01, 2024',
    },
    {
        id: '1026',
        title: 'Regular page 3',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 02, 2024',
    },
    {
        id: '1027',
        title: 'Product page 3',
        url: '/product/3',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Dec 03, 2024',
    },
    {
        id: '1028',
        title: 'Home page 3',
        url: '/home-3',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Dec 04, 2024',
    },
    {
        id: '1029',
        title: 'Landing page 2',
        url: '/landing/2',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 05, 2024',
    },
    {
        id: '1030',
        title: 'Regular page 4',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 06, 2024',
    },
    {
        id: '1031',
        title: 'Product page 4',
        url: '/product/4',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Dec 07, 2024',
    },
    {
        id: '1032',
        title: 'Home page 4',
        url: '/home-4',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Dec 08, 2024',
    },
    {
        id: '1033',
        title: 'Landing page 3',
        url: '/landing/3',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 09, 2024',
    },
    {
        id: '1034',
        title: 'Contact page 2',
        url: '/contact-2',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 10, 2024',
    },
    {
        id: '1035',
        title: 'About Us page',
        url: '/about',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 11, 2024',
    },
];

// Dữ liệu PageAnalytic mới với các trường analytics
export const PageAnalytics: PageAnalytic[] = [
    {
        id: '1020',
        title: 'Regular page 1',
        status: 'Unpublished',
        addToCartRate: 2.5,
        productViewsRate: 15.8,
        visitors: 1250,
        sessions: 1890,
        type: 'Landing',
        url: 'Not available',
        updated_at: 'Nov 26, 2024',
        analyticId: '1020',
    },
    {
        id: '1019',
        title: 'Home page 1',
        status: 'Published',
        addToCartRate: 4.2,
        productViewsRate: 28.5,
        visitors: 3450,
        sessions: 4120,
        type: 'Home',
        url: '/',
        updated_at: 'Nov 26, 2024',
        analyticId: '1019',
    },
    {
        id: '1018',
        title: 'Product page 1',
        status: 'Published',
        addToCartRate: 8.7,
        productViewsRate: 45.2,
        visitors: 2890,
        sessions: 3560,
        type: 'Product',
        url: '/product/1',
        updated_at: 'Nov 26, 2024',
        analyticId: '1018',
    },
    {
        id: '1021',
        title: 'Regular page 2',
        status: 'Unpublished',
        addToCartRate: 1.8,
        productViewsRate: 12.3,
        visitors: 890,
        sessions: 1120,
        type: 'Landing',
        url: 'Not available',
        updated_at: 'Nov 27, 2024',
        analyticId: '1021',
    },
    {
        id: '1022',
        title: 'Home page 2',
        status: 'Published',
        addToCartRate: 3.9,
        productViewsRate: 25.7,
        visitors: 2980,
        sessions: 3450,
        type: 'Home',
        url: '/home-2',
        updated_at: 'Nov 28, 2024',
        analyticId: '1022',
    },
    {
        id: '1023',
        title: 'Product page 2',
        status: 'Published',
        addToCartRate: 7.3,
        productViewsRate: 38.9,
        visitors: 2340,
        sessions: 2890,
        type: 'Product',
        url: '/product/2',
        updated_at: 'Nov 29, 2024',
        analyticId: '1023',
    },
    {
        id: '1024',
        title: 'Landing page 1',
        status: 'Unpublished',
        addToCartRate: 2.1,
        productViewsRate: 14.6,
        visitors: 1120,
        sessions: 1450,
        type: 'Landing',
        url: '/landing/1',
        updated_at: 'Nov 30, 2024',
        analyticId: '1024',
    },
    {
        id: '1025',
        title: 'Contact page',
        status: 'Published',
        addToCartRate: 0.5,
        productViewsRate: 8.2,
        visitors: 1890,
        sessions: 2120,
        type: 'Landing',
        url: '/contact',
        updated_at: 'Dec 01, 2024',
        analyticId: '1025',
    },
    {
        id: '1026',
        title: 'Regular page 3',
        status: 'Unpublished',
        addToCartRate: 1.9,
        productViewsRate: 11.8,
        visitors: 780,
        sessions: 950,
        type: 'Landing',
        url: 'Not available',
        updated_at: 'Dec 02, 2024',
        analyticId: '1026',
    },
    {
        id: '1027',
        title: 'Product page 3',
        status: 'Published',
        addToCartRate: 9.1,
        productViewsRate: 42.7,
        visitors: 3120,
        sessions: 3890,
        type: 'Product',
        url: '/product/3',
        updated_at: 'Dec 03, 2024',
        analyticId: '1027',
    },
];

export const PageStatusOptions = [
    {
        id: 'all',
        content: 'All',
    },
    {
        id: 'published',
        content: 'Published',
    },
    {
        id: 'unpublished',
        content: 'Unpublished',
    },
];
export const sortOptions: any[] = [
    { label: 'Title', value: 'order asc', directionLabel: 'Ascending' },
    { label: 'Title', value: 'order desc', directionLabel: 'Descending' },
    { label: 'Last updated', value: 'customer asc', directionLabel: 'A-Z' },
    { label: 'Last updated', value: 'customer desc', directionLabel: 'Z-A' },
];

export const filterChoices: FilterChoice[] = [
    { label: 'Published', value: 'Published' },
    { label: 'Unpublished', value: 'Unpublished' },
];

export const resourceName = {
    singular: 'Page',
    plural: 'Pages',
};

export const tableHeadings = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'Add to cart rate', alignment: 'center' },
    { title: 'Product views rate', alignment: 'center' },
    { title: 'Visitors', alignment: 'center' },
    { title: 'Sessions', alignment: 'center' },
    { title: '' },
];

// Cấu hình cũ cho tương thích ngược
export const tableHeadingsOld = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'Type', alignment: 'center' },
    { title: 'Last updated' },
    { title: '' },
];

export const tabs = [
    {
        id: 1,
        content: 'All',
        panelID: 'all',
    },
    {
        id: 2,
        content: 'Landing',
        panelID: 'landing',
    },
    {
        id: 3,
        content: 'Home',
        panelID: 'home',
    },
    {
        id: 4,
        content: 'Product',
        panelID: 'product',
    },
    {
        id: 5,
        content: 'Collection',
        panelID: 'collection',
    },
    {
        id: 6,
        content: 'List collection',
        panelID: 'list-collection',
    },
];
