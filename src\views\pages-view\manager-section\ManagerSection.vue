<script setup lang="ts">
import { Card } from '@ownego/polaris-vue';
import { ref, computed, onMounted, watch } from 'vue';

import type { PageItem } from '@/stores/pagesStore';

import { usePagesStore } from '@/stores/pagesStore';

import { resourceName, tableHeadings, PageStatusOptions, tabs, Page } from './configs';
import { ListBlock } from './list-block';
import { EmptyStateSnippet } from './list-block/empty-state-snippet';
import { PaginateBlock } from './paginate-block';
import { SkeletonBlock } from './skeleton-block';
import { TabBlock } from './tab-block';
import { useFilters } from './utils';

const pagesStore = usePagesStore();

onMounted(() => {
    if (pagesStore.pages.length === 0) {
        pagesStore.isLoading = true;

        setTimeout(() => {
            const convertedPages: PageItem[] = Page.map((page) => ({
                id: page.id,
                title: page.title,
                url: page.url,
                status: page.status,
                type: page.type,
                updated_at: page.Last_updated,
                created_at: page.Last_updated,
                analyticId: page.id,
            }));

            pagesStore.pages = convertedPages;
            pagesStore.isLoading = false;
        }, 1000);
    }
});

const activeTabId = ref(tabs[0]?.id);
const selected = ref(0);
const { queryValue, status, handleStatus, appliedFilters } = useFilters();

const listBlockRef = ref();

const typeMap: Record<string, string | undefined> = {
    landing: 'Landing',
    home: 'Home',
    product: 'Product',
    collection: 'Collection',
    'list-collection': 'List collection',
};

const filteredData = computed(() => {
    let filtered = [...pagesStore.pages];
    const activePanelID = tabs.find((tab) => tab.id === activeTabId.value)?.panelID;

    if (activePanelID && activePanelID !== 'all') {
        const typeFilter = typeMap[activePanelID];
        if (typeFilter) filtered = filtered.filter((page) => page.type === typeFilter);
    }

    const selectedOption = PageStatusOptions[selected.value];
    if (selectedOption?.content !== 'All') {
        filtered = filtered.filter((page) => page.status === selectedOption.content);
    }

    if (queryValue.value) {
        filtered = filtered.filter((page) => page.title.toLowerCase().includes(queryValue.value.toLowerCase()));
    }

    if (status.value.length > 0) {
        if (status.value.includes('All') && status.value.length === 1) return filtered;
        filtered = filtered.filter((page) => status.value.includes(page.status));
    }

    listBlockRef.value?.clearSelection();
    return filtered;
});

const currentPage = computed({
    get: () => pagesStore.currentPage,
    set: (value) => pagesStore.setCurrentPage(value),
});

const paginatedData = computed(() => {
    const startIndex = (currentPage.value - 1) * pagesStore.itemsPerPage;
    const endIndex = startIndex + pagesStore.itemsPerPage;
    return filteredData.value.slice(startIndex, endIndex);
});

const totalPages = computed(() => {
    return Math.max(1, Math.ceil(filteredData.value.length / pagesStore.itemsPerPage));
});

const goToPreviousPage = () => {
    pagesStore.goToPreviousPage();
};

const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
        pagesStore.goToNextPage();
    }
};

const handleTabClick = (tabId: number) => {
    activeTabId.value = tabId;
    pagesStore.setCurrentPage(1);
};

const handleFiltersSelect = (index: number) => {
    selected.value = index;
    pagesStore.setCurrentPage(1);
};

const handleFiltersQueryChange = (value: string) => {
    pagesStore.setQueryValue(value);
    pagesStore.setCurrentPage(1);
};

const updateStatus = (ids: string[], newStatus: string) => {
    ids.forEach((id) => {
        pagesStore.updatePageStatus(id, newStatus);
    });
};

watch(paginatedData, () => {
    listBlockRef.value?.clearSelection();
});
</script>

<template>
    <SkeletonBlock v-if="pagesStore.isLoading" />

    <template v-else>
        <Card v-if="pagesStore.pages.length > 0" padding="none">
            <TabBlock :activeTabId="activeTabId" :handleTabClick="handleTabClick" />
            <ListBlock ref="listBlockRef" :status="status" :appliedFilters="appliedFilters" :Page="paginatedData"
                :resourceName="resourceName" :tableHeadings="tableHeadings" :PageStatusOptions="PageStatusOptions"
                :selected="selected" :handleFiltersSelect="handleFiltersSelect" :update-status="updateStatus"
                :handleFiltersQueryChange="handleFiltersQueryChange" :handleStatus="handleStatus" />
            <PaginateBlock :currentPage="currentPage" :totalPages="totalPages" :totalItems="filteredData.length"
                :startItem="(currentPage - 1) * pagesStore.itemsPerPage + 1"
                :endItem="Math.min(currentPage * pagesStore.itemsPerPage, filteredData.length)"
                :goToPreviousPage="goToPreviousPage" :goToNextPage="goToNextPage" />
        </Card>

        <Card v-else padding="none">
            <EmptyStateSnippet />
        </Card>
    </template>
</template>

<style scoped lang="scss"></style>
