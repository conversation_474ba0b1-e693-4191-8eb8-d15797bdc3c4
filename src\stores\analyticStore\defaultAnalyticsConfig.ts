import type { ChartItem } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

// Default analytics data cho mỗi page - sẽ không random khi load lần đầu
export const defaultAnalyticsData = {
    // Manager view metrics - stable values
    addToCartRate: 85.2,
    productViewsRate: 92.7,
    visitors: 1234,
    sessions: 1572,

    // Detail view items - stable values từ config
    items: [
        {
            key: 'visitors',
            label: 'Visitors',
            value: 1234,
            percent: 8,
            lastPeriod: 1143,
            unit: '',
        },
        {
            key: 'sessions',
            label: 'Sessions',
            value: 1572,
            percent: 12,
            lastPeriod: 1403,
            unit: '',
        },
        {
            key: 'pageViews',
            label: 'Page views',
            value: 2847,
            percent: -3,
            lastPeriod: 2936,
            unit: '',
        },
        
    ] as ChartItem[],

    // Graph data - stable chart data
    graphData: {
        sales: [
            { date: '2024-01-01', value: 12 },
            { date: '2024-01-02', value: 15 },
            { date: '2024-01-03', value: 8 },
            { date: '2024-01-04', value: 18 },
            { date: '2024-01-05', value: 22 },
            { date: '2024-01-06', value: 16 },
            { date: '2024-01-07', value: 25 },
        ],
    },
};

// Function để tạo default analytics cho một page
export function createDefaultAnalyticsForPage(pageId: string): any {
    return {
        id: pageId,
        pageId: pageId,
        
        // Manager view metrics - sử dụng default values
        addToCartRate: defaultAnalyticsData.addToCartRate,
        productViewsRate: defaultAnalyticsData.productViewsRate,
        visitors: defaultAnalyticsData.visitors,
        sessions: defaultAnalyticsData.sessions,

        // Detail view data - sử dụng default values
        items: [...defaultAnalyticsData.items], // Clone array
        graphData: {
            sales: [...defaultAnalyticsData.graphData.sales], // Clone array
        },
    };
}
