import type { PageAnalytic } from './types';

/**
 * Utility functions để xử lý dữ liệu PageAnalytic
 */

/**
 * Format số với dấu phẩy phân cách hàng nghìn
 */
export function formatNumber(num: number): string {
    return num.toLocaleString();
}

/**
 * Format tỷ lệ phần trăm
 */
export function formatPercentage(rate: number, decimals: number = 1): string {
    return `${rate.toFixed(decimals)}%`;
}

/**
 * Tính toán tổng visitors từ danh sách PageAnalytic
 */
export function getTotalVisitors(pageAnalytics: PageAnalytic[]): number {
    return pageAnalytics.reduce((total, page) => total + page.visitors, 0);
}

/**
 * Tính toán tổng sessions từ danh sách PageAnalytic
 */
export function getTotalSessions(pageAnalytics: PageAnalytic[]): number {
    return pageAnalytics.reduce((total, page) => total + page.sessions, 0);
}

/**
 * <PERSON><PERSON>h toán tỷ lệ Add to cart trung bình
 */
export function getAverageAddToCartRate(pageAnalytics: PageAnalytic[]): number {
    if (pageAnalytics.length === 0) return 0;
    const total = pageAnalytics.reduce((sum, page) => sum + page.addToCartRate, 0);
    return total / pageAnalytics.length;
}

/**
 * Tính toán tỷ lệ Product views trung bình
 */
export function getAverageProductViewsRate(pageAnalytics: PageAnalytic[]): number {
    if (pageAnalytics.length === 0) return 0;
    const total = pageAnalytics.reduce((sum, page) => sum + page.productViewsRate, 0);
    return total / pageAnalytics.length;
}

/**
 * Lấy top N pages theo visitors
 */
export function getTopPagesByVisitors(pageAnalytics: PageAnalytic[], limit: number = 5): PageAnalytic[] {
    return [...pageAnalytics]
        .sort((a, b) => b.visitors - a.visitors)
        .slice(0, limit);
}

/**
 * Lấy top N pages theo add to cart rate
 */
export function getTopPagesByAddToCartRate(pageAnalytics: PageAnalytic[], limit: number = 5): PageAnalytic[] {
    return [...pageAnalytics]
        .sort((a, b) => b.addToCartRate - a.addToCartRate)
        .slice(0, limit);
}

/**
 * Lấy top N pages theo product views rate
 */
export function getTopPagesByProductViewsRate(pageAnalytics: PageAnalytic[], limit: number = 5): PageAnalytic[] {
    return [...pageAnalytics]
        .sort((a, b) => b.productViewsRate - a.productViewsRate)
        .slice(0, limit);
}

/**
 * Filter pages theo status
 */
export function filterPagesByStatus(pageAnalytics: PageAnalytic[], status: string): PageAnalytic[] {
    if (status === 'All') return pageAnalytics;
    return pageAnalytics.filter(page => page.status === status);
}

/**
 * Filter pages theo type
 */
export function filterPagesByType(pageAnalytics: PageAnalytic[], type: string): PageAnalytic[] {
    if (type === 'All') return pageAnalytics;
    return pageAnalytics.filter(page => page.type === type);
}

/**
 * Search pages theo title
 */
export function searchPagesByTitle(pageAnalytics: PageAnalytic[], query: string): PageAnalytic[] {
    if (!query.trim()) return pageAnalytics;
    const lowerQuery = query.toLowerCase();
    return pageAnalytics.filter(page => 
        page.title.toLowerCase().includes(lowerQuery)
    );
}

/**
 * Sort pages theo field và direction
 */
export function sortPages(
    pageAnalytics: PageAnalytic[], 
    field: keyof PageAnalytic, 
    direction: 'asc' | 'desc' = 'desc'
): PageAnalytic[] {
    return [...pageAnalytics].sort((a, b) => {
        const aValue = a[field];
        const bValue = b[field];
        
        if (typeof aValue === 'number' && typeof bValue === 'number') {
            return direction === 'asc' ? aValue - bValue : bValue - aValue;
        }
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
            return direction === 'asc' 
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        }
        
        return 0;
    });
}

/**
 * Tính conversion rate từ visitors sang sessions
 */
export function getVisitorToSessionRate(visitors: number, sessions: number): number {
    if (visitors === 0) return 0;
    return (sessions / visitors) * 100;
}

/**
 * Validate PageAnalytic data
 */
export function validatePageAnalytic(page: Partial<PageAnalytic>): boolean {
    return !!(
        page.id &&
        page.title &&
        page.status &&
        typeof page.addToCartRate === 'number' &&
        typeof page.productViewsRate === 'number' &&
        typeof page.visitors === 'number' &&
        typeof page.sessions === 'number'
    );
}

/**
 * Generate mock PageAnalytic data
 */
export function generateMockPageAnalytic(id: string, title: string): PageAnalytic {
    return {
        id,
        title,
        status: Math.random() > 0.5 ? 'Published' : 'Unpublished',
        addToCartRate: Math.round(Math.random() * 10 * 100) / 100,
        productViewsRate: Math.round(Math.random() * 50 * 100) / 100,
        visitors: Math.floor(Math.random() * 5000) + 100,
        sessions: Math.floor(Math.random() * 7000) + 150,
        type: ['Landing', 'Home', 'Product', 'Collection'][Math.floor(Math.random() * 4)],
        url: `/${title.toLowerCase().replace(/\s+/g, '-')}`,
        updated_at: new Date().toLocaleDateString(),
        analyticId: id,
    };
}
