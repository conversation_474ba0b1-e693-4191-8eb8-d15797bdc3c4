<script setup lang="ts">
import { Page } from '@ownego/polaris-vue';
import { onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';

import { useAnalyticStore } from '@/stores/analyticStore';

import { ChartBlock } from './chart-block';
import { FilterDateBlock } from './filter-date-block';

const analyticStore = useAnalyticStore();
const route = useRoute();

const pageId = computed(() => route.params.id as string | undefined);


onMounted(async () => {
    await analyticStore.fetchPageAnalytics();

    if (pageId.value) {
        const analytic = analyticStore.getAnalyticByPageId(pageId.value);
        if (analytic) {
            analyticStore.setCurrentAnalytic(analytic.id);
        }
    }

    // Tải dữ liệu ban đầu khi component được mount
    await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);

    // Mặc định chọn item đầu tiên
    if (analyticStore.items.length > 0) {
        analyticStore.setActiveDataKey(analyticStore.items[0].key);
    }
});



</script>

<template>
    <Page :title="'Analytic Detail'" :subtitle="pageId" :back-action="{ content: 'Back', url: '/analytics' }">
        <div class="analytic-container">
            <FilterDateBlock />
            <chartBlock />
        </div>
    </Page>
</template>

<style scoped>
.analytic-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
</style>
