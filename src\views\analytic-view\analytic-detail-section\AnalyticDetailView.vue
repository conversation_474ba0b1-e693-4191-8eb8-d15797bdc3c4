<script setup lang="ts">
import { Page } from '@ownego/polaris-vue';
import { onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useAnalyticStore } from '@/stores/analyticStore';

import { ChartBlock } from './chart-block';
import { FilterDateBlock } from './filter-date-block';

const analyticStore = useAnalyticStore();
const route = useRoute();
const router = useRouter();

const pageId = computed(() => route.params.id as string | undefined);


onMounted(async () => {
    // Kiểm tra xem đã có data chưa
    const hasAnalyticsList = analyticStore.analyticList.length > 0;

    if (pageId.value) {
        // Thử load data ngay lập tức trước nếu đã có analytics list
        if (hasAnalyticsList) {
            const analytic = analyticStore.getAnalyticByPageId(pageId.value);
            if (analytic) {
                const loadedInstantly = analyticStore.loadAnalyticDataInstantly(analytic.id);

                if (loadedInstantly) {
                    // Data đã load ngay lập tức, không cần fetch thêm
                    return;
                }
            }
        }

        // Nếu chưa có analytics list hoặc chưa load được data ngay lập tức
        if (!hasAnalyticsList) {
            await analyticStore.fetchPageAnalytics();
        }

        const analytic = analyticStore.getAnalyticByPageId(pageId.value);
        if (analytic) {
            analyticStore.setCurrentAnalytic(analytic.id);
            await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);

            if (analyticStore.items.length > 0) {
                analyticStore.setActiveDataKey(analyticStore.items[0].key);
            }
        }
    } else {
        // Nếu không có pageId, load data mặc định
        if (!hasAnalyticsList) {
            await analyticStore.fetchPageAnalytics();
        }

        if (analyticStore.items.length === 0) {
            await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);
        }

        if (analyticStore.items.length > 0) {
            analyticStore.setActiveDataKey(analyticStore.items[0].key);
        }
    }
});



</script>

<template>
    <Page :title="'Analytic Detail'" :subtitle="pageId" :back-action="{
        content: 'Back', onAction() {
            router.push('/analytics')
        },
    }">
        <div class="analytic-container">
            <FilterDateBlock />
            <chartBlock />
        </div>
    </Page>
</template>

<style scoped>
.analytic-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
</style>
