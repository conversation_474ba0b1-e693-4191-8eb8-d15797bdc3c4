<script setup lang="ts">
import { Footer } from './footer';
import { MiniMenu } from './mini-menu';
</script>

<template>
    <main>
        <ui-nav-menu>
            <router-link to="/" rel="home" :aria-current="null">Home</router-link>
            <router-link to="/pages" :aria-current="null">Pages</router-link>
            <router-link to="/forms" :aria-current="null">Forms</router-link>
            <router-link to="/settings" :aria-current="null">Settings</router-link>
        </ui-nav-menu>

        <MiniMenu />
        <router-view />
        <Footer />
    </main>
</template>

<style scoped lang="scss"></style>
