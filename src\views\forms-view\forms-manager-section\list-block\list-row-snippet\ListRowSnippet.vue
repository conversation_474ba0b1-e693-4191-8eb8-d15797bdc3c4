<script setup lang="ts">
import { IndexTableRow, IndexTableCell, Text, Badge, InlineStack, Icon, Box } from '@ownego/polaris-vue';
import ChartPopularIcon from '@shopify/polaris-icons/ChartPopularIcon.svg';
import ComposeIcon from '@shopify/polaris-icons/ComposeIcon.svg';
import DatabaseIcon from '@shopify/polaris-icons/DatabaseIcon.svg';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { formsLocaleVars } from '@/locales/forms';
import router from '@/router';

import type { ListRowSnippetProps } from './types';

const { t } = useI18n();
const props = defineProps<ListRowSnippetProps>();

const getTranslatedStatus = computed(() => {
    return props.formData.status === 'Published'
        ? t(formsLocaleVars.formsStatusPublished)
        : props.formData.status === 'Unpublished'
          ? t(formsLocaleVars.formsStatusUnpublished)
          : props.formData.status;
});

const handleRowClick = (id: string, event: Event) => {
    router.push(`/submissions/${id}`);
    event.stopPropagation();
};

const handleIconClick = (event: Event) => {
    event.stopPropagation();
    const modal = document.getElementById('test-modal') as HTMLDialogElement;
    if (modal) {
        modal.show();
    }
};
</script>

<template>
    <IndexTableRow :id="formData.form_id" :key="formData.form_id" :position="index" :selected="selected">
        <IndexTableCell class="cell__wrapper first-cell" @click="handleRowClick(formData.form_id, $event)">
            <Text as="span" variant="headingSm" font-weight="medium" class="clickable-text">{{
                formData.form_id
            }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper second-cell" @click="handleRowClick(formData.form_id, $event)">
            <Text variant="bodyMd" fontWeight="medium" as="span" class="clickable-text">{{ formData.formtitle }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper third-cell" @click="handleRowClick(formData.form_id, $event)">
            <div class="badge__wrapper">
                <div :class="`badge__wrapper__${formData.status}`">
                    <Badge :progress="formData.status === 'Published' ? 'complete' : 'incomplete'">
                        {{ getTranslatedStatus }}
                    </Badge>
                </div>
            </div>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper fourth-cell" @click="handleRowClick(formData.form_id, $event)">
            <Text as="span" alignment="center" numeric class="clickable-text">{{ formData.submissions_count }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper fifth-cell" @click="handleRowClick(formData.form_id, $event)">
            <span class="clickable-text">{{ formData.Last_updated }}</span>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper sixth-cell" @click="handleIconClick($event)">
            <Box>
                <InlineStack gap="500">
                    <a @click="handleIconClick($event)">
                        <Icon :source="ComposeIcon" />
                    </a>
                    <a @click="handleIconClick($event)">
                        <Icon :source="DatabaseIcon" />
                    </a>

                    <a @click="handleIconClick($event)">
                        <Icon :source="ChartPopularIcon" />
                    </a>
                </InlineStack>
            </Box>
        </IndexTableCell>
    </IndexTableRow>
</template>

<style scoped lang="scss">
.cell__wrapper {
    height: 52px;

    a {
        text-decoration: none;
        color: var(--m-black-icon);
    }
}

.clickable-text {
    cursor: pointer;
    display: inline-block;
}

.first-cell {
    width: 14%;
}

.second-cell {
    width: 33%;
}

.third-cell {
    width: 12%;
}

.fourth-cell {
    width: 10%;
    color: var(--m-shared-base);
    text-align: center;
}

.fifth-cell {
    width: 14%;
}

.sixth-cell {
    width: 13%;
}

.badge__wrapper {
    cursor: pointer;

    &__Published {
        .Polaris-Badge {
            background-color: var(--m-success-base-20);
            color: var(--m-success-base-30);

            svg {
                fill: var(--m-success-base-30);
            }
        }
    }

    &__Unpublished {
        .Polaris-Badge {
            background-color: var(--m-overlay-color-2);
        }
    }
}
</style>
