import { formsLocaleVars } from '@/locales/forms';

import type { FilterChoice, SortOption, SubmissionData, Tab, FormData } from './types';

export const SubmissionDatas: SubmissionData[] = [
    {
        id: '1',
        form_id: 'MF_CONTACT',
        gmail: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        status: 'Read',
        Last_updated: '2024-06-25',
    },
    {
        id: '2',
        form_id: 'MF_CONTACT',
        gmail: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        status: 'Read',
        Last_updated: '2024-06-25',
    },
    {
        id: '3',
        form_id: 'MF_CONTACT',
        gmail: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        status: 'Unread',
        Last_updated: '2024-06-25',
    },
    {
        id: '4',
        form_id: 'MF_SUPPORT',
        gmail: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        status: 'Read',
        Last_updated: '2024-06-24',
    },
    {
        id: '5',
        form_id: 'MF_SUPPORT',
        gmail: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        status: 'Unread',
        Last_updated: '2024-06-24',
    },
    {
        id: '6',
        form_id: 'MF_JOB_APP',
        gmail: '<EMAIL>',
        first_name: 'Frank',
        last_name: 'Lee',
        status: 'Read',
        Last_updated: '2024-06-22',
    },
    {
        id: '7',
        form_id: 'MF_JOB_APP',
        gmail: '<EMAIL>',
        first_name: 'Georgia',
        last_name: 'Clark',
        status: 'Read',
        Last_updated: '2024-06-22',
    },
];

export const FormDatas: FormData[] = [
    {
        form_id: 'MF_CONTACT',
        formtitle: 'Contact Us',
        status: 'Published',
        submissions_count: 3,
        Last_updated: '2024-06-25',
    },
    {
        form_id: 'MF_SUPPORT',
        formtitle: 'Support Request',
        status: 'Published',
        submissions_count: 2,
        Last_updated: '2024-06-24',
    },
    {
        form_id: 'MF_FEEDBACK',
        formtitle: 'Feedback Form',
        status: 'Unpublished',
        submissions_count: 0,
        Last_updated: '2024-06-23',
    },
    {
        form_id: 'MF_JOB_APP',
        formtitle: 'Job Application',
        status: 'Published',
        submissions_count: 2,
        Last_updated: '2024-06-22',
    },
];

export const SubmissionDataStatusOptions = [
    {
        id: 'all',
        content: 'All',
    },
    {
        id: 'read',
        content: 'Read',
    },
    {
        id: 'unread',
        content: 'Unread',
    },
];

export const FormStatusOptions = [
    {
        id: 'all',
        content: 'All',
    },
    {
        id: 'published',
        content: 'Published',
    },
    {
        id: 'unpublished',
        content: 'Unpublished',
    },
];

export const submissionFilterChoices: FilterChoice[] = [
    { label: 'Read', value: 'Read' },
    { label: 'Unread', value: 'Unread' },
];

export const formFilterChoices: FilterChoice[] = [
    { label: 'Published', value: 'Published' },
    { label: 'Unpublished', value: 'Unpublished' },
];

export const sortOptions: SortOption[] = [
    { label: 'Order', value: 'order asc', directionLabel: 'Ascending' },
    { label: 'Order', value: 'order desc', directionLabel: 'Descending' },
    { label: 'Customer', value: 'customer asc', directionLabel: 'A-Z' },
    { label: 'Customer', value: 'customer desc', directionLabel: 'Z-A' },
    { label: 'Date', value: 'date asc', directionLabel: 'A-Z' },
    { label: 'Date', value: 'date desc', directionLabel: 'Z-A' },
    { label: 'Total', value: 'total asc', directionLabel: 'Ascending' },
    { label: 'Total', value: 'total desc', directionLabel: 'Descending' },
];

export const resourceName = {
    singular: 'order',
    plural: 'orders',
};

export const formTableHeadings = [
    { title: 'Form ID' },
    { title: 'Form Title' },
    { title: 'Status' },
    { title: 'Submissions' },
    { title: 'Last updated' },
    { title: '' },
];

export const tabs: Tab[] = [
    {
        id: 1,
        content: 'All',
        panelID: 'all',
    },
    {
        id: 2,
        content: 'Regular',
        panelID: 'regular',
    },
    {
        id: 3,
        content: 'Home',
        panelID: 'home',
    },
    {
        id: 4,
        content: 'Product',
        panelID: 'product',
    },
    {
        id: 5,
        content: 'Collection',
        panelID: 'collection',
    },
    {
        id: 6,
        content: 'List Collection',
        panelID: 'list-collection',
    },
];

export const formsTabs = [
    { id: 'Forms', label: formsLocaleVars.forms },
    { id: 'Submissions', label: formsLocaleVars.submissions },
];
