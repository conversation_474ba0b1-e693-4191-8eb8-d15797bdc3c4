<script setup lang="ts">
import { Divider, IndexFilters, IndexTable, useIndexResourceState, useSetIndexFiltersMode } from '@ownego/polaris-vue';
import { ref, computed, h, resolveComponent } from 'vue';
import { useI18n } from 'vue-i18n';

import { ModalBase } from '@/components/modal-base';
import { formsLocaleVars } from '@/locales/forms';

import { formFilterChoices as filterChoices } from '../../configs';
import { useFilters } from '../utils';
import { ListRowSnippet } from './list-row-snippet';

const { t } = useI18n();
const sortSelected = ref(['order asc']);
const { mode, setMode } = useSetIndexFiltersMode();

const props = defineProps<{
    formData: any[];
    resourceName: any;
    tableHeadings: any[];
    FormDataStatusOptions: any[];
    selected: number;
    status: string[];
    handleFiltersSelect: (_value: any) => void;
    handleStatus: (_value: string[]) => void;
    handleFiltersQueryChange: (_value: any) => void;
    handleQueryValueRemove: () => void;
    handleFiltersClearAll: () => void;
    appliedFilters: any[];
    updateStatus: (_ids: string[], _newStatus: string) => void;
}>();

const { queryValue } = useFilters();
const currentData = computed(() => props.formData);

const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } = useIndexResourceState(
    currentData,
    {
        resourceIDResolver(resource) {
            return resource.form_id;
        },
    },
);

defineExpose({ clearSelection });

const filters = computed(() => [
    {
        name: 'status',
        label: t(formsLocaleVars.formsStatusLabel),
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: t(formsLocaleVars.formsStatusLabel),
                titleHidden: true,
                choices: filterChoices.map((choice) => ({
                    ...choice,
                    label:
                        choice.value === 'All'
                            ? t(formsLocaleVars.formsStatusAll)
                            : choice.value === 'Published'
                              ? t(formsLocaleVars.formsStatusPublished)
                              : choice.value === 'Unpublished'
                                ? t(formsLocaleVars.formsStatusUnpublished)
                                : choice.label,
                })),
                modelValue: props.status || [],
                onChange: props.handleStatus,
                allowMultiple: true,
            }),
        shortcut: true,
    },
]);

const onHandleCancel = () => {};

const handleFiltersSort = (value: string[]) => {
    sortSelected.value = value;
};

const selectedRows = computed(() =>
    props.formData.filter((row) => selectedResources.value.includes(String(row.form_id))),
);

const hasMixedStatus = computed(() => {
    const statuses = selectedRows.value.map((row) => row.status);
    return new Set(statuses).size > 1;
});

const allArePublished = computed(
    () => selectedRows.value.length > 0 && selectedRows.value.every((row) => row.status === 'Published'),
);

const allAreUnpublished = computed(
    () => selectedRows.value.length > 0 && selectedRows.value.every((row) => row.status === 'Unpublished'),
);

const promotedBulkActions = computed(() => [
    {
        content: t(formsLocaleVars.formsActionsPublish),
        onAction: () => props.updateStatus(selectedResources.value, 'Published'),
        disabled: allArePublished.value,
    },
    {
        content: t(formsLocaleVars.formsActionsUnpublish),
        onAction: () => props.updateStatus(selectedResources.value, 'Unpublished'),
        disabled: hasMixedStatus.value || allAreUnpublished.value,
    },
]);

const sortOptions = computed<any[]>(() => [
    { label: t(formsLocaleVars.formsTableFormTitle), value: 'order asc', directionLabel: 'Ascending' },
    { label: t(formsLocaleVars.formsTableFormTitle), value: 'order desc', directionLabel: 'Descending' },
    { label: t(formsLocaleVars.formsTableLastUpdated), value: 'customer asc', directionLabel: 'A-Z' },
    { label: t(formsLocaleVars.formsTableLastUpdated), value: 'customer desc', directionLabel: 'Z-A' },
]);

const translatedStatusOptions = computed(() =>
    props.FormDataStatusOptions.map((option) => ({
        ...option,
        content:
            option.content === 'All'
                ? t(formsLocaleVars.formsStatusAll)
                : option.content === 'Published'
                  ? t(formsLocaleVars.formsStatusPublished)
                  : option.content === 'Unpublished'
                    ? t(formsLocaleVars.formsStatusUnpublished)
                    : option.content,
    })),
);

const translatedHeadings = computed(() =>
    props.tableHeadings.map((heading, index) => {
        if (index === 0) return { ...heading, title: t(formsLocaleVars.formsTableFormId) };
        if (index === 1) return { ...heading, title: t(formsLocaleVars.formsTableFormTitle) };
        if (index === 2) return { ...heading, title: t(formsLocaleVars.formsTableStatus) };
        if (index === 3) return { ...heading, title: t(formsLocaleVars.formsTableSubmissions) };
        if (index === 4) return { ...heading, title: t(formsLocaleVars.formsTableLastUpdated) };
        return heading;
    }),
);
</script>

<template>
    <ModalBase :id="'test-modal'" :title="'Test Modal'" :primary-action="'Test Primary Action'"> </ModalBase>
    <IndexFilters
        :sortOptions="sortOptions"
        :sortSelected="sortSelected"
        :queryValue="queryValue"
        :queryPlaceholder="t(formsLocaleVars.formsSearchPlaceholder)"
        :cancelAction="{ onAction: onHandleCancel, disabled: false, loading: false }"
        :tabs="translatedStatusOptions"
        :selected="selected"
        :filters="filters"
        :appliedFilters="appliedFilters"
        :mode="mode"
        @set-mode="setMode"
        @query-change="handleFiltersQueryChange"
        @query-clear="handleQueryValueRemove"
        @sort="handleFiltersSort"
        @select="handleFiltersSelect"
        @clear-all="handleFiltersClearAll"
        :can-create-new-view="false"
    />

    <IndexTable
        :condensed="false"
        :resourceName="resourceName"
        :itemCount="formData.length"
        :selectedItemsCount="allResourcesSelected ? 'All' : selectedResources.length"
        @selection-change="handleSelectionChange"
        :headings="translatedHeadings"
        :promoted-bulk-actions="promotedBulkActions"
    >
        <ListRowSnippet
            v-for="(row, index) in props.formData"
            :key="row.form_id"
            :formData="row"
            :index="index"
            :selected="selectedResources.includes(String(row.form_id))"
        />
    </IndexTable>

    <Divider borderColor="border" />
</template>

<style scoped lang="scss"></style>
