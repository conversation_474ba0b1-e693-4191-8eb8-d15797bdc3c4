import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

import type { PageAnalyticItem } from './types';

export const usePageAnalyticStore = defineStore('pageAnalytic', () => {
    const pageAnalytics = ref<PageAnalyticItem[]>([]);
    const isLoading = ref(false);
    const queryValue = ref('');
    const status = ref<string[]>([]);
    const currentPage = ref(1);
    const itemsPerPage = ref(3);
    const activeTab = ref('All');

    const filteredPageAnalytics = computed(() => {
        let filtered = [...pageAnalytics.value];

        if (activeTab.value !== 'All') {
            filtered = filtered.filter((page) => page.type === activeTab.value);
        }

        if (status.value.length > 0) {
            if (status.value.includes('All') && status.value.length === 1) {
                return filtered;
            }
            filtered = filtered.filter((page) => status.value.includes(page.status));
        }

        if (queryValue.value) {
            filtered = filtered.filter((page) => 
                page.title?.toLowerCase().includes(queryValue.value.toLowerCase())
            );
        }

        return filtered;
    });

    const paginatedPageAnalytics = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredPageAnalytics.value.slice(start, end);
    });

    const totalPages = computed(() => 
        Math.max(1, Math.ceil(filteredPageAnalytics.value.length / itemsPerPage.value))
    );

    const totalItems = computed(() => filteredPageAnalytics.value.length);

    const startItem = computed(() => (currentPage.value - 1) * itemsPerPage.value + 1);

    const endItem = computed(() => 
        Math.min(startItem.value + itemsPerPage.value - 1, totalItems.value)
    );

    // Actions
    function fetchPageAnalytics() {
        isLoading.value = true;

        setTimeout(() => {
            // Mock data sẽ được load từ configs
            isLoading.value = false;
        }, 1000);
    }

    function getPageAnalyticById(id: string): PageAnalyticItem | undefined {
        return pageAnalytics.value.find((page) => page.id === id);
    }

    function setActiveTab(tab: string) {
        activeTab.value = tab;
        currentPage.value = 1;
    }

    function setQueryValue(value: string) {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatus(newStatus: string[]) {
        status.value = newStatus;
        currentPage.value = 1;
    }

    function clearStatus() {
        status.value = [];
    }

    function clearQueryValue() {
        queryValue.value = '';
    }

    function clearAllFilters() {
        status.value = [];
        queryValue.value = '';
    }

    function setCurrentPage(page: number) {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
        }
    }

    function goToPreviousPage() {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage() {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updatePageAnalyticStatus(id: string, newStatus: string) {
        const pageIndex = pageAnalytics.value.findIndex((page) => page.id === id);
        if (pageIndex !== -1) {
            pageAnalytics.value[pageIndex] = {
                ...pageAnalytics.value[pageIndex],
                status: newStatus,
            };
        }
    }

    function deletePageAnalytic(id: string) {
        pageAnalytics.value = pageAnalytics.value.filter((page) => page.id !== id);
    }

    function updatePageAnalyticData(id: string, data: Partial<PageAnalyticItem>) {
        const pageIndex = pageAnalytics.value.findIndex((page) => page.id === id);
        if (pageIndex !== -1) {
            pageAnalytics.value[pageIndex] = {
                ...pageAnalytics.value[pageIndex],
                ...data,
            };
        }
    }

    return {
        pageAnalytics,
        isLoading,
        queryValue,
        status,
        currentPage,
        itemsPerPage,
        activeTab,

        filteredPageAnalytics,
        paginatedPageAnalytics,
        totalPages,
        totalItems,
        startItem,
        endItem,

        fetchPageAnalytics,
        getPageAnalyticById,
        setActiveTab,
        setQueryValue,
        setStatus,
        clearStatus,
        clearQueryValue,
        clearAllFilters,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updatePageAnalyticStatus,
        deletePageAnalytic,
        updatePageAnalyticData,
    };
});
