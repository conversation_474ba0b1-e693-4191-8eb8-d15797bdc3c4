export interface Pages {
    [key: string]: unknown;
    id: string;
    title: string;
    url: string;
    status: string;
    type: string;
    Last_updated: string;
    analyticId?: string;
}

export interface PageAnalytic {
    id: string;
    title: string;
    status: string;
    addToCartRate: number; // Tỷ lệ thêm vào giỏ hàng (%)
    productViewsRate: number; // Tỷ lệ xem sản phẩm (%)
    visitors: number; // Số lượng khách truy cập
    sessions: number; // Số phiên làm việc
    type?: string; // Lo<PERSON><PERSON> trang (optional để tương thích)
    url?: string; // URL trang (optional để tương thích)
    updated_at?: string; // Thời gian cập nhật (optional để tương thích)
    analyticId?: string; // ID analytics liên kết
}

export interface FilterChoice {
    label: string;
    value: string;
}

export interface Tab {
    content: string;
    index: number;
    onAction: () => void;
    id: string;
    isLocked: boolean;
    actions: any[];
}

export interface AppliedFilter {
    name: string;
    label: string;
    onRemove: () => void;
}
